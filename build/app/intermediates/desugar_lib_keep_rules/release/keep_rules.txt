-keep class j$.time.Duration {
  public static j$.time.Duration ofMillis(long);
  public long toMillis();
}
-keep class j$.time.Instant {
  public j$.time.OffsetDateTime atOffset(j$.time.ZoneOffset);
}
-keep class j$.time.LocalDateTime {
  public java.lang.String format(j$.time.format.DateTimeFormatter);
}
-keep class j$.time.OffsetDateTime {
  public j$.time.LocalDateTime toLocalDateTime();
}
-keep class j$.time.TimeConversions {
  public static java.time.Duration convert(j$.time.Duration);
  public static j$.time.Duration convert(java.time.Duration);
}
-keep class j$.time.ZoneOffset {
  j$.time.ZoneOffset UTC;
}
-keep class j$.time.format.DateTimeFormatter {
  j$.time.format.DateTimeFormatter ISO_LOCAL_DATE;
}
-keep class j$.util.Collection$-EL {
  public static j$.util.stream.Stream stream(java.util.Collection);
}
-keep class j$.util.Comparator$-CC {
  public static java.util.Comparator comparing(java.util.function.Function,java.util.Comparator);
}
-keep class j$.util.DateRetargetClass {
  public static j$.time.Instant toInstant(java.util.Date);
}
-keep class j$.util.DesugarCollections {
  public static java.util.Map synchronizedMap(java.util.Map);
}
-keep class j$.util.DesugarTimeZone {
  public static java.util.TimeZone getTimeZone(java.lang.String);
}
-keep interface j$.util.Map {
  public java.lang.Object compute(java.lang.Object,java.util.function.BiFunction);
  public java.lang.Object computeIfAbsent(java.lang.Object,java.util.function.Function);
  public java.lang.Object computeIfPresent(java.lang.Object,java.util.function.BiFunction);
  public void forEach(java.util.function.BiConsumer);
  public java.lang.Object getOrDefault(java.lang.Object,java.lang.Object);
  public java.lang.Object merge(java.lang.Object,java.lang.Object,java.util.function.BiFunction);
  public java.lang.Object putIfAbsent(java.lang.Object,java.lang.Object);
  public boolean remove(java.lang.Object,java.lang.Object);
  public java.lang.Object replace(java.lang.Object,java.lang.Object);
  public boolean replace(java.lang.Object,java.lang.Object,java.lang.Object);
  public void replaceAll(java.util.function.BiFunction);
}
-keep class j$.util.Map$-CC {
  public static java.lang.Object $default$compute(java.util.Map,java.lang.Object,java.util.function.BiFunction);
  public static java.lang.Object $default$computeIfAbsent(java.util.Map,java.lang.Object,java.util.function.Function);
  public static java.lang.Object $default$computeIfPresent(java.util.Map,java.lang.Object,java.util.function.BiFunction);
  public static void $default$forEach(java.util.Map,java.util.function.BiConsumer);
  public static java.lang.Object $default$merge(java.util.Map,java.lang.Object,java.lang.Object,java.util.function.BiFunction);
  public static java.lang.Object $default$putIfAbsent(java.util.Map,java.lang.Object,java.lang.Object);
  public static boolean $default$remove(java.util.Map,java.lang.Object,java.lang.Object);
  public static java.lang.Object $default$replace(java.util.Map,java.lang.Object,java.lang.Object);
  public static boolean $default$replace(java.util.Map,java.lang.Object,java.lang.Object,java.lang.Object);
  public static void $default$replaceAll(java.util.Map,java.util.function.BiFunction);
}
-keep class j$.util.Map$-EL {
  public static java.lang.Object getOrDefault(java.util.Map,java.lang.Object,java.lang.Object);
}
-keep class j$.util.Objects {
  public static boolean equals(java.lang.Object,java.lang.Object);
  public static int hash(java.lang.Object[]);
  public static int hashCode(java.lang.Object);
  public static java.lang.Object requireNonNull(java.lang.Object);
  public static java.lang.Object requireNonNull(java.lang.Object,java.lang.String);
  public static java.lang.String toString(java.lang.Object);
  public static java.lang.String toString(java.lang.Object,java.lang.String);
}
-keep class j$.util.concurrent.ConcurrentHashMap {
  public <init>();
  public <init>(int,float,int);
  public <init>(java.util.Map);
  public boolean containsKey(java.lang.Object);
  public java.lang.Object get(java.lang.Object);
  public java.util.Set keySet();
  public java.lang.Object put(java.lang.Object,java.lang.Object);
  public java.lang.Object putIfAbsent(java.lang.Object,java.lang.Object);
  public java.lang.Object remove(java.lang.Object);
  public boolean remove(java.lang.Object,java.lang.Object);
  public java.util.Collection values();
}
-keep class j$.util.concurrent.ThreadLocalRandom {
  public static j$.util.concurrent.ThreadLocalRandom current();
}
-keep class j$.util.function.Function$-CC {
  public static java.util.function.Function $default$andThen(java.util.function.Function,java.util.function.Function);
  public static java.util.function.Function $default$compose(java.util.function.Function,java.util.function.Function);
}
-keep class j$.util.function.Predicate$-CC {
  public static java.util.function.Predicate $default$and(java.util.function.Predicate,java.util.function.Predicate);
  public static java.util.function.Predicate $default$negate(java.util.function.Predicate);
  public static java.util.function.Predicate $default$or(java.util.function.Predicate,java.util.function.Predicate);
}
-keep interface j$.util.stream.IntStream {
}
-keep class j$.util.stream.IntStream$VivifiedWrapper {
  public static j$.util.stream.IntStream convert(java.util.stream.IntStream);
}
-keep class j$.util.stream.IntStream$Wrapper {
  public static java.util.stream.IntStream convert(j$.util.stream.IntStream);
}
-keep interface j$.util.stream.Stream {
  public boolean anyMatch(java.util.function.Predicate);
}
-keep interface java.util.function.BiConsumer {
}
-keep interface java.util.function.BiFunction {
}
-keep interface java.util.function.Consumer {
}
-keep interface java.util.function.Function {
}
-keep interface java.util.function.Predicate {
}
