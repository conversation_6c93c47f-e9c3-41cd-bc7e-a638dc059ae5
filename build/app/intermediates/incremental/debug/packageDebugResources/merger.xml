<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/59/android/app/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/59/android/app/src/main/res"><file name="ic_launcher" path="/Users/<USER>/Desktop/59/android/app/src/main/res/mipmap-mdpi/ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="launcher_icon" path="/Users/<USER>/Desktop/59/android/app/src/main/res/mipmap-mdpi/launcher_icon.png" qualifiers="mdpi-v4" type="mipmap"/><file name="splash" path="/Users/<USER>/Desktop/59/android/app/src/main/res/drawable-night-xxhdpi/splash.png" qualifiers="night-xxhdpi-v8" type="drawable"/><file name="ic_launcher" path="/Users/<USER>/Desktop/59/android/app/src/main/res/mipmap-hdpi/ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="launcher_icon" path="/Users/<USER>/Desktop/59/android/app/src/main/res/mipmap-hdpi/launcher_icon.png" qualifiers="hdpi-v4" type="mipmap"/><file path="/Users/<USER>/Desktop/59/android/app/src/main/res/values-night-v31/styles.xml" qualifiers="night-v31"><style name="LaunchTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file name="background" path="/Users/<USER>/Desktop/59/android/app/src/main/res/drawable-night/background.png" qualifiers="night-v8" type="drawable"/><file name="launch_background" path="/Users/<USER>/Desktop/59/android/app/src/main/res/drawable-night/launch_background.xml" qualifiers="night-v8" type="drawable"/><file name="app_icon" path="/Users/<USER>/Desktop/59/android/app/src/main/res/drawable/app_icon.jpg" qualifiers="" type="drawable"/><file name="background" path="/Users/<USER>/Desktop/59/android/app/src/main/res/drawable/background.png" qualifiers="" type="drawable"/><file name="launch_background" path="/Users/<USER>/Desktop/59/android/app/src/main/res/drawable/launch_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="/Users/<USER>/Desktop/59/android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="launcher_icon" path="/Users/<USER>/Desktop/59/android/app/src/main/res/mipmap-xxxhdpi/launcher_icon.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Users/<USER>/Desktop/59/android/app/src/main/res/mipmap-xxhdpi/ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="launcher_icon" path="/Users/<USER>/Desktop/59/android/app/src/main/res/mipmap-xxhdpi/launcher_icon.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="splash" path="/Users/<USER>/Desktop/59/android/app/src/main/res/drawable-night-mdpi/splash.png" qualifiers="night-mdpi-v8" type="drawable"/><file path="/Users/<USER>/Desktop/59/android/app/src/main/res/values-night/styles.xml" qualifiers="night-v8"><style name="LaunchTheme" parent="@android:style/Theme.Black.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file name="background" path="/Users/<USER>/Desktop/59/android/app/src/main/res/drawable-night-v21/background.png" qualifiers="night-v21" type="drawable"/><file name="launch_background" path="/Users/<USER>/Desktop/59/android/app/src/main/res/drawable-night-v21/launch_background.xml" qualifiers="night-v21" type="drawable"/><file path="/Users/<USER>/Desktop/59/android/app/src/main/res/values/colors.xml" qualifiers=""><color name="notification_color">#6366F1</color></file><file path="/Users/<USER>/Desktop/59/android/app/src/main/res/values/styles.xml" qualifiers=""><style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file name="splash" path="/Users/<USER>/Desktop/59/android/app/src/main/res/drawable-night-hdpi/splash.png" qualifiers="night-hdpi-v8" type="drawable"/><file name="splash" path="/Users/<USER>/Desktop/59/android/app/src/main/res/drawable-xhdpi/splash.png" qualifiers="xhdpi-v4" type="drawable"/><file name="splash" path="/Users/<USER>/Desktop/59/android/app/src/main/res/drawable-night-xhdpi/splash.png" qualifiers="night-xhdpi-v8" type="drawable"/><file name="splash" path="/Users/<USER>/Desktop/59/android/app/src/main/res/drawable-xxhdpi/splash.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="network_security_config" path="/Users/<USER>/Desktop/59/android/app/src/main/res/xml/network_security_config.xml" qualifiers="" type="xml"/><file name="splash" path="/Users/<USER>/Desktop/59/android/app/src/main/res/drawable-hdpi/splash.png" qualifiers="hdpi-v4" type="drawable"/><file name="background" path="/Users/<USER>/Desktop/59/android/app/src/main/res/drawable-v21/background.png" qualifiers="v21" type="drawable"/><file name="launch_background" path="/Users/<USER>/Desktop/59/android/app/src/main/res/drawable-v21/launch_background.xml" qualifiers="v21" type="drawable"/><file name="splash" path="/Users/<USER>/Desktop/59/android/app/src/main/res/drawable-mdpi/splash.png" qualifiers="mdpi-v4" type="drawable"/><file name="splash" path="/Users/<USER>/Desktop/59/android/app/src/main/res/drawable-night-xxxhdpi/splash.png" qualifiers="night-xxxhdpi-v8" type="drawable"/><file name="ic_launcher" path="/Users/<USER>/Desktop/59/android/app/src/main/res/mipmap-xhdpi/ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="launcher_icon" path="/Users/<USER>/Desktop/59/android/app/src/main/res/mipmap-xhdpi/launcher_icon.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="splash" path="/Users/<USER>/Desktop/59/android/app/src/main/res/drawable-xxxhdpi/splash.png" qualifiers="xxxhdpi-v4" type="drawable"/><file path="/Users/<USER>/Desktop/59/android/app/src/main/res/values-v31/styles.xml" qualifiers="v31"><style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file name="ic_notification" path="/Users/<USER>/Desktop/59/android/app/src/main/res/drawable/ic_notification.xml" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/59/android/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/59/android/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/59/build/app/generated/res/resValues/debug"/><source path="/Users/<USER>/Desktop/59/build/app/generated/res/google-services/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/59/build/app/generated/res/resValues/debug"/><source path="/Users/<USER>/Desktop/59/build/app/generated/res/google-services/debug"><file path="/Users/<USER>/Desktop/59/build/app/generated/res/google-services/debug/values/values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">103777508111</string><string name="google_api_key" translatable="false">AIzaSyA4omCq_KZJ54x_kY1vylIguopsUVfnGJg</string><string name="google_app_id" translatable="false">1:103777508111:android:5b02cd9d47aad8c0f5fc7a</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyA4omCq_KZJ54x_kY1vylIguopsUVfnGJg</string><string name="google_storage_bucket" translatable="false">kft-notfication.firebasestorage.app</string><string name="project_id" translatable="false">kft-notfication</string></file></source></dataSet><mergedItems/></merger>