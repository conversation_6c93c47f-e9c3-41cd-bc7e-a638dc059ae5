1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.kft"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:2:5-66
15-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:2:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:3:5-78
16-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:3:22-76
17    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
17-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:4:5-76
17-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:4:22-74
18    <uses-permission android:name="android.permission.WAKE_LOCK" />
18-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:5:5-67
18-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:5:22-65
19    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
19-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:6:5-80
19-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:6:22-78
20    <uses-permission android:name="android.permission.VIBRATE" />
20-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:7:5-65
20-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:7:22-63
21    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
21-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:8:5-78
21-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:8:22-76
22    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
22-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:9:5-73
22-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:9:22-71
23    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
23-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:10:5-76
23-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:10:22-74
24    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" /> <!-- Additional critical permissions for notification reliability -->
24-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:11:5-94
24-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:11:22-92
25    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
25-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:14:5-81
25-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:14:22-78
26    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
26-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:15:5-78
26-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:15:22-75
27    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
27-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:16:5-89
27-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:16:22-86
28    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
28-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:17:5-75
28-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:17:22-72
29    <uses-permission android:name="android.permission.TURN_SCREEN_ON" />
29-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:18:5-73
29-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:18:22-70
30    <!--
31 Required to query activities that can process text, see:
32         https://developer.android.com/training/package-visibility and
33         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
34
35         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
36    -->
37    <queries>
37-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:57:5-77:15
38        <intent>
38-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:58:9-61:18
39            <action android:name="android.intent.action.PROCESS_TEXT" />
39-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:59:13-72
39-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:59:21-70
40
41            <data android:mimeType="text/plain" />
41-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:60:13-50
41-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:60:19-48
42        </intent>
43        <!-- Add WhatsApp package query -->
44        <package android:name="com.whatsapp" />
44-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:63:9-48
44-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:63:18-45
45        <!-- Add URL schemes for WhatsApp -->
46        <intent>
46-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:65:9-68:18
47            <action android:name="android.intent.action.VIEW" />
47-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:66:13-65
47-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:66:21-62
48
49            <data android:scheme="https" />
49-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:60:13-50
50        </intent>
51        <intent>
51-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:69:9-72:18
52            <action android:name="android.intent.action.VIEW" />
52-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:66:13-65
52-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:66:21-62
53
54            <data android:scheme="http" />
54-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:60:13-50
55        </intent>
56        <intent>
56-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:73:9-76:18
57            <action android:name="android.intent.action.VIEW" />
57-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:66:13-65
57-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:66:21-62
58
59            <data android:scheme="whatsapp" />
59-->/Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:60:13-50
60        </intent>
61        <intent>
61-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-10:18
62            <action android:name="android.support.customtabs.action.CustomTabsService" />
62-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:9:13-90
62-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:9:21-87
63        </intent>
64    </queries> <!-- Required by older versions of Google Play services to create IID tokens -->
65    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
65-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:26:5-82
65-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:26:22-79
66    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
66-->[com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:25:5-79
66-->[com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:25:22-76
67    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
67-->[com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:26:5-88
67-->[com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:26:22-85
68    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
68-->[com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:27:5-82
68-->[com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:27:22-79
69    <uses-permission android:name="android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS" />
69-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-89
69-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:7:22-86
70    <uses-permission
70-->[me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:9:5-12:47
71        android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE"
71-->[me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:10:9-77
72        android:maxSdkVersion="22" />
72-->[me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:11:9-35
73    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
73-->[com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:26:5-110
73-->[com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:26:22-107
74
75    <permission
75-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
76        android:name="com.example.kft.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
76-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
77        android:protectionLevel="signature" />
77-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
78
79    <uses-permission android:name="com.example.kft.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- for android -->
79-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
79-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
80    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
81    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
82    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
83    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
84    <!-- for Samsung -->
85    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
85-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:19:5-86
85-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:19:22-83
86    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
86-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:20:5-87
86-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:20:22-84
87    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
87-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:23:5-81
87-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:23:22-78
88    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
88-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:24:5-83
88-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:24:22-80
89    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
89-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:27:5-88
89-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:27:22-85
90    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
90-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:28:5-92
90-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:28:22-89
91    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
91-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:31:5-84
91-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:31:22-81
92    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
92-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:34:5-83
92-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:34:22-80
93    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
93-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:37:5-91
93-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:37:22-88
94    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
94-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:38:5-92
94-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:38:22-89
95    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
95-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:39:5-93
95-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:39:22-90
96    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
96-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:42:5-73
96-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:42:22-70
97    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
97-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:45:5-82
97-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:45:22-79
98    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
98-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:46:5-83
98-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:46:22-80
99    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
99-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:49:5-88
99-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:49:22-85
100    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
100-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:50:5-89
100-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:50:22-86
101
102    <application
103        android:name="android.app.Application"
104        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
104-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
105        android:debuggable="true"
106        android:extractNativeLibs="false"
107        android:icon="@mipmap/launcher_icon"
108        android:label="kft"
109        android:networkSecurityConfig="@xml/network_security_config" >
110        <activity
111            android:name="com.example.kft.MainActivity"
112            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
113            android:exported="true"
114            android:hardwareAccelerated="true"
115            android:launchMode="singleTop"
116            android:taskAffinity=""
117            android:theme="@style/LaunchTheme"
118            android:windowSoftInputMode="adjustResize" >
119
120            <!--
121                 Specifies an Android theme to apply to this Activity as soon as
122                 the Android process has started. This theme is visible to the user
123                 while the Flutter UI initializes. After that, this theme continues
124                 to determine the Window background behind the Flutter UI.
125            -->
126            <meta-data
127                android:name="io.flutter.embedding.android.NormalTheme"
128                android:resource="@style/NormalTheme" />
129
130            <intent-filter>
131                <action android:name="android.intent.action.MAIN" />
132
133                <category android:name="android.intent.category.LAUNCHER" />
134            </intent-filter>
135        </activity>
136        <!--
137             Don't delete the meta-data below.
138             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
139        -->
140        <meta-data
141            android:name="flutterEmbedding"
142            android:value="2" />
143        <!--
144           Declares a provider which allows us to store files to share in
145           '.../caches/share_plus' and grant the receiving action access
146        -->
147        <provider
147-->[:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:13:9-21:20
148            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
148-->[:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-77
149            android:authorities="com.example.kft.flutter.share_provider"
149-->[:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-74
150            android:exported="false"
150-->[:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:16:13-37
151            android:grantUriPermissions="true" >
151-->[:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:17:13-47
152            <meta-data
152-->[:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:18:13-20:68
153                android:name="android.support.FILE_PROVIDER_PATHS"
153-->[:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:19:17-67
154                android:resource="@xml/flutter_share_file_paths" />
154-->[:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:20:17-65
155        </provider>
156        <!--
157           This manifest declared broadcast receiver allows us to use an explicit
158           Intent when creating a PendingItent to be informed of the user's choice
159        -->
160        <receiver
160-->[:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:26:9-32:20
161            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
161-->[:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:27:13-82
162            android:exported="false" >
162-->[:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:28:13-37
163            <intent-filter>
163-->[:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:29:13-31:29
164                <action android:name="EXTRA_CHOSEN_COMPONENT" />
164-->[:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:30:17-65
164-->[:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:30:25-62
165            </intent-filter>
166        </receiver>
167
168        <service
168-->[:firebase_analytics] /Users/<USER>/Desktop/59/build/firebase_analytics/intermediates/merged_manifest/debug/AndroidManifest.xml:12:9-16:19
169            android:name="com.google.firebase.components.ComponentDiscoveryService"
169-->[:firebase_analytics] /Users/<USER>/Desktop/59/build/firebase_analytics/intermediates/merged_manifest/debug/AndroidManifest.xml:12:18-89
170            android:directBootAware="true"
170-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:32:13-43
171            android:exported="false" >
171-->[com.google.firebase:firebase-analytics-ktx:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/c8b8cf0be5b39564d368c9c946066111/transformed/firebase-analytics-ktx-22.4.0/AndroidManifest.xml:10:13-37
172            <meta-data
172-->[:firebase_analytics] /Users/<USER>/Desktop/59/build/firebase_analytics/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-15:85
173                android:name="com.google.firebase.components:io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar"
173-->[:firebase_analytics] /Users/<USER>/Desktop/59/build/firebase_analytics/intermediates/merged_manifest/debug/AndroidManifest.xml:14:17-128
174                android:value="com.google.firebase.components.ComponentRegistrar" />
174-->[:firebase_analytics] /Users/<USER>/Desktop/59/build/firebase_analytics/intermediates/merged_manifest/debug/AndroidManifest.xml:15:17-82
175            <meta-data
175-->[:firebase_core] /Users/<USER>/Desktop/59/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:9:13-11:85
176                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
176-->[:firebase_core] /Users/<USER>/Desktop/59/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:10:17-124
177                android:value="com.google.firebase.components.ComponentRegistrar" />
177-->[:firebase_core] /Users/<USER>/Desktop/59/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:11:17-82
178            <meta-data
178-->[com.google.firebase:firebase-analytics-ktx:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/c8b8cf0be5b39564d368c9c946066111/transformed/firebase-analytics-ktx-22.4.0/AndroidManifest.xml:11:13-13:85
179                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
179-->[com.google.firebase:firebase-analytics-ktx:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/c8b8cf0be5b39564d368c9c946066111/transformed/firebase-analytics-ktx-22.4.0/AndroidManifest.xml:12:17-129
180                android:value="com.google.firebase.components.ComponentRegistrar" />
180-->[com.google.firebase:firebase-analytics-ktx:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/c8b8cf0be5b39564d368c9c946066111/transformed/firebase-analytics-ktx-22.4.0/AndroidManifest.xml:13:17-82
181            <meta-data
181-->[com.google.firebase:firebase-messaging-ktx:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/72e57fc24a13f1cb41009954b9a12311/transformed/firebase-messaging-ktx-24.1.1/AndroidManifest.xml:26:13-28:85
182                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar"
182-->[com.google.firebase:firebase-messaging-ktx:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/72e57fc24a13f1cb41009954b9a12311/transformed/firebase-messaging-ktx-24.1.1/AndroidManifest.xml:27:17-129
183                android:value="com.google.firebase.components.ComponentRegistrar" />
183-->[com.google.firebase:firebase-messaging-ktx:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/72e57fc24a13f1cb41009954b9a12311/transformed/firebase-messaging-ktx-24.1.1/AndroidManifest.xml:28:17-82
184            <meta-data
184-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:57:13-59:85
185                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
185-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:58:17-122
186                android:value="com.google.firebase.components.ComponentRegistrar" />
186-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:59:17-82
187            <meta-data
187-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:60:13-62:85
188                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
188-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:61:17-119
189                android:value="com.google.firebase.components.ComponentRegistrar" />
189-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:62:17-82
190            <meta-data
190-->[com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:33:13-35:85
191                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
191-->[com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:34:17-139
192                android:value="com.google.firebase.components.ComponentRegistrar" />
192-->[com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:35:17-82
193            <meta-data
193-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:15:13-17:85
194                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
194-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:16:17-130
195                android:value="com.google.firebase.components.ComponentRegistrar" />
195-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:17:17-82
196            <meta-data
196-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:18:13-20:85
197                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
197-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:19:17-127
198                android:value="com.google.firebase.components.ComponentRegistrar" />
198-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:20:17-82
199            <meta-data
199-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/e9fa5516e1bccc9a746fa45b1bb2aaf9/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
200                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
200-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/e9fa5516e1bccc9a746fa45b1bb2aaf9/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
201                android:value="com.google.firebase.components.ComponentRegistrar" />
201-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/e9fa5516e1bccc9a746fa45b1bb2aaf9/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
202            <meta-data
202-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
203                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
203-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:36:17-109
204                android:value="com.google.firebase.components.ComponentRegistrar" />
204-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:37:17-82
205            <meta-data
205-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/d8217dc7863bca0dfd259441afb86ea5/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
206                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
206-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/d8217dc7863bca0dfd259441afb86ea5/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
207                android:value="com.google.firebase.components.ComponentRegistrar" />
207-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/d8217dc7863bca0dfd259441afb86ea5/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
208        </service>
209
210        <receiver
210-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:29:9-40:20
211            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
211-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:30:13-78
212            android:exported="true"
212-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:31:13-36
213            android:permission="com.google.android.c2dm.permission.SEND" >
213-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:32:13-73
214            <intent-filter>
214-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:33:13-35:29
215                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
215-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:34:17-81
215-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:34:25-78
216            </intent-filter>
217
218            <meta-data
218-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:37:13-39:40
219                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
219-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:38:17-92
220                android:value="true" />
220-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:39:17-37
221        </receiver>
222        <!--
223             FirebaseMessagingService performs security checks at runtime,
224             but set to not exported to explicitly avoid allowing another app to call it.
225        -->
226        <service
226-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:46:9-53:19
227            android:name="com.google.firebase.messaging.FirebaseMessagingService"
227-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:47:13-82
228            android:directBootAware="true"
228-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:48:13-43
229            android:exported="false" >
229-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:49:13-37
230            <intent-filter android:priority="-500" >
230-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:50:13-52:29
230-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:50:28-51
231                <action android:name="com.google.firebase.MESSAGING_EVENT" />
231-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:51:17-78
231-->[com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:51:25-75
232            </intent-filter>
233        </service>
234
235        <provider
235-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
236            android:name="com.google.firebase.provider.FirebaseInitProvider"
236-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:24:13-77
237            android:authorities="com.example.kft.firebaseinitprovider"
237-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:25:13-72
238            android:directBootAware="true"
238-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:26:13-43
239            android:exported="false"
239-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:27:13-37
240            android:initOrder="100" />
240-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:28:13-36
241        <provider
241-->[:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:9:9-17:20
242            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
242-->[:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:10:13-82
243            android:authorities="com.example.kft.flutter.image_provider"
243-->[:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-74
244            android:exported="false"
244-->[:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-37
245            android:grantUriPermissions="true" >
245-->[:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-47
246            <meta-data
246-->[:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:18:13-20:68
247                android:name="android.support.FILE_PROVIDER_PATHS"
247-->[:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:19:17-67
248                android:resource="@xml/flutter_image_picker_file_paths" />
248-->[:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:20:17-65
249        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
250        <service
250-->[:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:19:9-31:19
251            android:name="com.google.android.gms.metadata.ModuleDependencies"
251-->[:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-78
252            android:enabled="false"
252-->[:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:21:13-36
253            android:exported="false" >
253-->[:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:22:13-37
254            <intent-filter>
254-->[:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:24:13-26:29
255                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
255-->[:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:25:17-94
255-->[:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:25:25-91
256            </intent-filter>
257
258            <meta-data
258-->[:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:28:13-30:36
259                android:name="photopicker_activity:0:required"
259-->[:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:29:17-63
260                android:value="" />
260-->[:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:30:17-33
261        </service>
262
263        <activity
263-->[:url_launcher_android] /Users/<USER>/Desktop/59/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-11:74
264            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
264-->[:url_launcher_android] /Users/<USER>/Desktop/59/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:9:13-74
265            android:exported="false"
265-->[:url_launcher_android] /Users/<USER>/Desktop/59/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:10:13-37
266            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
266-->[:url_launcher_android] /Users/<USER>/Desktop/59/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-71
267
268        <receiver
268-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:10:9-12:39
269            android:name="me.carda.awesome_notifications.DartNotificationActionReceiver"
269-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-89
270            android:exported="true" />
270-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-36
271        <receiver
271-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:13:9-15:39
272            android:name="me.carda.awesome_notifications.DartDismissedNotificationReceiver"
272-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-92
273            android:exported="true" />
273-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-36
274        <receiver
274-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:16:9-18:39
275            android:name="me.carda.awesome_notifications.DartScheduledNotificationReceiver"
275-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:17:13-92
276            android:exported="true" />
276-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:18:13-36
277        <receiver
277-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:19:9-33:20
278            android:name="me.carda.awesome_notifications.DartRefreshSchedulesReceiver"
278-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-87
279            android:enabled="true"
279-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:21:13-35
280            android:exported="true" >
280-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:22:13-36
281            <intent-filter>
281-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:23:13-32:29
282                <category android:name="android.intent.category.DEFAULT" />
282-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:24:17-76
282-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:24:27-73
283
284                <action android:name="android.intent.action.BOOT_COMPLETED" />
284-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:26:17-79
284-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:26:25-76
285                <action android:name="android.intent.action.LOCKED_BOOT_COMPLETED" />
285-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:27:17-86
285-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:27:25-83
286                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
286-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:28:17-84
286-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:28:25-81
287                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
287-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:29:17-82
287-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:29:25-79
288                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
288-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:30:17-82
288-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:30:25-79
289                <action android:name="android.app.action.SCHEDULE_EXACT_ALARM_PERMISSION_STATE_CHANGED" />
289-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:31:17-107
289-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:31:25-104
290            </intent-filter>
291        </receiver>
292
293        <service
293-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:35:9-38:72
294            android:name="me.carda.awesome_notifications.DartBackgroundService"
294-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:36:13-80
295            android:exported="false"
295-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:37:13-37
296            android:permission="android.permission.BIND_JOB_SERVICE" />
296-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:38:13-69
297        <service
297-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:39:9-44:43
298            android:name="me.carda.awesome_notifications.core.services.ForegroundService"
298-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:40:13-90
299            android:enabled="true"
299-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:41:13-35
300            android:exported="false"
300-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:42:13-37
301            android:foregroundServiceType="phoneCall"
301-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:43:13-54
302            android:stopWithTask="true" />
302-->[:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:44:13-40
303
304        <activity
304-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:14:9-18:47
305            android:name="com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity"
305-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-112
306            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density"
306-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:16:13-137
307            android:exported="false"
307-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:17:13-37
308            android:theme="@style/AppTheme" />
308-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:18:13-44
309        <activity
309-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:19:9-22:55
310            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity"
310-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-120
311            android:exported="false"
311-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:21:13-37
312            android:theme="@style/ThemeTransparent" />
312-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:22:13-52
313        <activity
313-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:23:9-26:55
314            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity"
314-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:24:13-114
315            android:exported="false"
315-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:25:13-37
316            android:theme="@style/ThemeTransparent" />
316-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:26:13-52
317        <activity
317-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:27:9-31:55
318            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance"
318-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:28:13-134
319            android:exported="false"
319-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:29:13-37
320            android:launchMode="singleInstance"
320-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:30:13-48
321            android:theme="@style/ThemeTransparent" />
321-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:31:13-52
322        <activity
322-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:32:9-36:55
323            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance"
323-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:33:13-128
324            android:exported="false"
324-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:34:13-37
325            android:launchMode="singleInstance"
325-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:35:13-48
326            android:theme="@style/ThemeTransparent" />
326-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:36:13-52
327
328        <receiver
328-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:38:9-41:40
329            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver"
329-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:39:13-119
330            android:enabled="true"
330-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:40:13-35
331            android:exported="false" />
331-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:41:13-37
332
333        <meta-data
333-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:43:9-45:36
334            android:name="io.flutter.embedded_views_preview"
334-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:44:13-61
335            android:value="true" />
335-->[:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:45:13-33
336
337        <service
337-->[me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:21:9-28:19
338            android:name="me.carda.awesome_notifications.core.managers.StatusBarManager"
338-->[me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:22:13-89
339            android:exported="true"
339-->[me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:23:13-36
340            android:label="My Notification Listener Service" >
340-->[me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:24:13-61
341            <intent-filter>
341-->[me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:25:13-27:29
342                <action android:name="android.service.notification.NotificationListenerService" />
342-->[me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:26:17-99
342-->[me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:26:25-96
343            </intent-filter>
344        </service>
345
346        <receiver
346-->[com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:29:9-33:20
347            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
347-->[com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:30:13-85
348            android:enabled="true"
348-->[com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:31:13-35
349            android:exported="false" >
349-->[com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:32:13-37
350        </receiver>
351
352        <service
352-->[com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:35:9-38:40
353            android:name="com.google.android.gms.measurement.AppMeasurementService"
353-->[com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:36:13-84
354            android:enabled="true"
354-->[com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:37:13-35
355            android:exported="false" />
355-->[com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:38:13-37
356        <service
356-->[com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:39:9-43:72
357            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
357-->[com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:40:13-87
358            android:enabled="true"
358-->[com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:41:13-35
359            android:exported="false"
359-->[com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:42:13-37
360            android:permission="android.permission.BIND_JOB_SERVICE" />
360-->[com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:43:13-69
361
362        <activity
362-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/ce98a87c43f97d057c48b3433fc10c79/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:9-173
363            android:name="com.google.android.gms.common.api.GoogleApiActivity"
363-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/ce98a87c43f97d057c48b3433fc10c79/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:19-85
364            android:exported="false"
364-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/ce98a87c43f97d057c48b3433fc10c79/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:146-170
365            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
365-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/ce98a87c43f97d057c48b3433fc10c79/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:86-145
366
367        <provider
367-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/16693b5e1a09ace5ff4ca3456cfe7953/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
368            android:name="androidx.startup.InitializationProvider"
368-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/16693b5e1a09ace5ff4ca3456cfe7953/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
369            android:authorities="com.example.kft.androidx-startup"
369-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/16693b5e1a09ace5ff4ca3456cfe7953/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
370            android:exported="false" >
370-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/16693b5e1a09ace5ff4ca3456cfe7953/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
371            <meta-data
371-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/16693b5e1a09ace5ff4ca3456cfe7953/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
372                android:name="androidx.emoji2.text.EmojiCompatInitializer"
372-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/16693b5e1a09ace5ff4ca3456cfe7953/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
373                android:value="androidx.startup" />
373-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/16693b5e1a09ace5ff4ca3456cfe7953/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
374            <meta-data
374-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/066ad0703a4a4a7e35fddd8e645492a3/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
375                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
375-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/066ad0703a4a4a7e35fddd8e645492a3/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
376                android:value="androidx.startup" />
376-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/066ad0703a4a4a7e35fddd8e645492a3/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
377            <meta-data
377-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
378                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
378-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
379                android:value="androidx.startup" />
379-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
380        </provider>
381
382        <uses-library
382-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b21b8ff5629daa6ae82a2981003c3e1/transformed/window-1.2.0/AndroidManifest.xml:23:9-25:40
383            android:name="androidx.window.extensions"
383-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b21b8ff5629daa6ae82a2981003c3e1/transformed/window-1.2.0/AndroidManifest.xml:24:13-54
384            android:required="false" />
384-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b21b8ff5629daa6ae82a2981003c3e1/transformed/window-1.2.0/AndroidManifest.xml:25:13-37
385        <uses-library
385-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b21b8ff5629daa6ae82a2981003c3e1/transformed/window-1.2.0/AndroidManifest.xml:26:9-28:40
386            android:name="androidx.window.sidecar"
386-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b21b8ff5629daa6ae82a2981003c3e1/transformed/window-1.2.0/AndroidManifest.xml:27:13-51
387            android:required="false" />
387-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b21b8ff5629daa6ae82a2981003c3e1/transformed/window-1.2.0/AndroidManifest.xml:28:13-37
388        <uses-library
388-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/transforms-3/1f7636bc79bcca6cfc240d5569ab97dd/transformed/ads-adservices-1.1.0-beta11/AndroidManifest.xml:23:9-25:40
389            android:name="android.ext.adservices"
389-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/transforms-3/1f7636bc79bcca6cfc240d5569ab97dd/transformed/ads-adservices-1.1.0-beta11/AndroidManifest.xml:24:13-50
390            android:required="false" />
390-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/transforms-3/1f7636bc79bcca6cfc240d5569ab97dd/transformed/ads-adservices-1.1.0-beta11/AndroidManifest.xml:25:13-37
391
392        <meta-data
392-->[com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/7178fedea55c950c8017bbfb028a9590/transformed/play-services-basement-18.5.0/AndroidManifest.xml:21:9-23:69
393            android:name="com.google.android.gms.version"
393-->[com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/7178fedea55c950c8017bbfb028a9590/transformed/play-services-basement-18.5.0/AndroidManifest.xml:22:13-58
394            android:value="@integer/google_play_services_version" />
394-->[com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/7178fedea55c950c8017bbfb028a9590/transformed/play-services-basement-18.5.0/AndroidManifest.xml:23:13-66
395
396        <service
396-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/48b2b5e224a2f110a57147aa68b2a2c6/transformed/room-runtime-2.6.1/AndroidManifest.xml:24:9-28:63
397            android:name="androidx.room.MultiInstanceInvalidationService"
397-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/48b2b5e224a2f110a57147aa68b2a2c6/transformed/room-runtime-2.6.1/AndroidManifest.xml:25:13-74
398            android:directBootAware="true"
398-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/48b2b5e224a2f110a57147aa68b2a2c6/transformed/room-runtime-2.6.1/AndroidManifest.xml:26:13-43
399            android:exported="false" />
399-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/48b2b5e224a2f110a57147aa68b2a2c6/transformed/room-runtime-2.6.1/AndroidManifest.xml:27:13-37
400
401        <receiver
401-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
402            android:name="androidx.profileinstaller.ProfileInstallReceiver"
402-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
403            android:directBootAware="false"
403-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
404            android:enabled="true"
404-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
405            android:exported="true"
405-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
406            android:permission="android.permission.DUMP" >
406-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
407            <intent-filter>
407-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
408                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
408-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
408-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
409            </intent-filter>
410            <intent-filter>
410-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
411                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
411-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
411-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
412            </intent-filter>
413            <intent-filter>
413-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
414                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
414-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
414-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
415            </intent-filter>
416            <intent-filter>
416-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
417                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
417-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
417-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
418            </intent-filter>
419        </receiver>
420
421        <service
421-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
422            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
422-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
423            android:exported="false" >
423-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
424            <meta-data
424-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
425                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
425-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
426                android:value="cct" />
426-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
427        </service>
428        <service
428-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
429            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
429-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
430            android:exported="false"
430-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
431            android:permission="android.permission.BIND_JOB_SERVICE" >
431-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
432        </service>
433
434        <receiver
434-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
435            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
435-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:33:13-132
436            android:exported="false" />
436-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
437    </application>
438
439</manifest>
