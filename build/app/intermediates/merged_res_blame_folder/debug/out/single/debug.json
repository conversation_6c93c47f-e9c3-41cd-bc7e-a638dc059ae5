[{"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/mipmap-xxhdpi_launcher_icon.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/mipmap-xxhdpi/launcher_icon.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/mipmap-hdpi_ic_launcher.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/mipmap-hdpi/ic_launcher.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/drawable-night-v21_background.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/drawable-night-v21/background.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/drawable-night-hdpi_splash.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/drawable-night-hdpi/splash.png"}, {"merged": "com.example.kft.app-merged_res-66:/drawable_ic_notification.xml.flat", "source": "com.example.kft.app-main-59:/drawable/ic_notification.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/drawable-night-v21_launch_background.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/drawable-night-v21/launch_background.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/mipmap-xhdpi_launcher_icon.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/mipmap-xhdpi/launcher_icon.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/drawable_app_icon.jpg.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/drawable/app_icon.jpg"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/drawable-night-xxxhdpi_splash.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/drawable-night-xxxhdpi/splash.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/mipmap-xhdpi_ic_launcher.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/xml_network_security_config.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/xml/network_security_config.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/drawable-xxhdpi_splash.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/drawable-xxhdpi/splash.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/drawable-mdpi_splash.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/drawable-mdpi/splash.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/drawable-hdpi_splash.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/drawable-hdpi/splash.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/drawable-xxxhdpi_splash.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/drawable-xxxhdpi/splash.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/drawable-xhdpi_splash.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/drawable-xhdpi/splash.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/mipmap-mdpi_ic_launcher.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/mipmap-mdpi/ic_launcher.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/drawable-night-xxhdpi_splash.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/drawable-night-xxhdpi/splash.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/drawable-v21_background.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/drawable-v21/background.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/mipmap-xxxhdpi_launcher_icon.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/mipmap-xxxhdpi/launcher_icon.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/mipmap-mdpi_launcher_icon.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/mipmap-mdpi/launcher_icon.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/drawable-night-mdpi_splash.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/drawable-night-mdpi/splash.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/drawable-v21_launch_background.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/drawable-v21/launch_background.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/mipmap-hdpi_launcher_icon.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/mipmap-hdpi/launcher_icon.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-merged_res-66:/drawable-night-xhdpi_splash.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.4/com.example.kft.app-main-59:/drawable-night-xhdpi/splash.png"}]