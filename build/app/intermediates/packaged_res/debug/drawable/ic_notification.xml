<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
    
    <!-- Background circle with gradient -->
    <path
        android:pathData="M12,2C6.48,2 2,6.48 2,12s4.48,10 10,10s10,-4.48 10,-10S17.52,2 12,2z"
        android:fillColor="#FF6B35"/>
    
    <!-- Inner circle for depth -->
    <path
        android:pathData="M12,3.5C7.31,3.5 3.5,7.31 3.5,12s3.81,8.5 8.5,8.5s8.5,-3.81 8.5,-8.5S16.69,3.5 12,3.5z"
        android:fillColor="#FF8A65"/>
    
    <!-- Fitness/workout icon -->
    <path
        android:pathData="M20.57,14.86L22,13.43L20.57,12L17,15.57L8.43,7L12,3.43L10.57,2L9.14,3.43L7.71,2L5.57,4.14L4.14,2.71L2.71,4.14L4.14,5.57L2,7.71L3.43,9.14L2,10.57L3.43,12L7,8.43L15.57,17L12,20.57L13.43,22L14.86,20.57L16.29,22L18.43,19.86L19.86,21.29L21.29,19.86L19.86,18.43L22,16.29L20.57,14.86Z"
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.9"/>
    
    <!-- Simplified dumbbell icon for better visibility -->
    <group
        android:translateX="12"
        android:translateY="12">
        <path
            android:pathData="M-6,-1.5L-6,1.5L-4,1.5L-4,2.5L4,2.5L4,1.5L6,1.5L6,-1.5L4,-1.5L4,-2.5L-4,-2.5L-4,-1.5L-6,-1.5Z"
            android:fillColor="#FFFFFF"/>
        <circle
            android:cx="-5"
            android:cy="0"
            android:radius="2"
            android:fillColor="#FFFFFF"/>
        <circle
            android:cx="5"
            android:cy="0"
            android:radius="2"
            android:fillColor="#FFFFFF"/>
    </group>
</vector>
