  ActivityManager android.app  ActivityInfo android.app.Activity  Build android.app.Activity  DeviceInfoPlugin android.app.Activity  
MethodChannel android.app.Activity  PerformancePlugin android.app.Activity  WebSettings android.app.Activity  
WindowManager android.app.Activity  requestedOrientation android.app.Activity  window android.app.Activity  
MemoryInfo android.app.ActivityManager  
getMemoryInfo android.app.ActivityManager  largeMemoryClass android.app.ActivityManager  memoryClass android.app.ActivityManager  availMem &android.app.ActivityManager.MemoryInfo  	lowMemory &android.app.ActivityManager.MemoryInfo  	threshold &android.app.ActivityManager.MemoryInfo  totalMem &android.app.ActivityManager.MemoryInfo  Context android.content  ACTIVITY_SERVICE android.content.Context  ActivityInfo android.content.Context  Build android.content.Context  DeviceInfoPlugin android.content.Context  
MethodChannel android.content.Context  PerformancePlugin android.content.Context  WebSettings android.content.Context  
WindowManager android.content.Context  getSystemService android.content.Context  ActivityInfo android.content.ContextWrapper  Build android.content.ContextWrapper  DeviceInfoPlugin android.content.ContextWrapper  
MethodChannel android.content.ContextWrapper  PerformancePlugin android.content.ContextWrapper  WebSettings android.content.ContextWrapper  
WindowManager android.content.ContextWrapper  ActivityInfo android.content.pm  SCREEN_ORIENTATION_LANDSCAPE android.content.pm.ActivityInfo  SCREEN_ORIENTATION_PORTRAIT android.content.pm.ActivityInfo  SCREEN_ORIENTATION_UNSPECIFIED android.content.pm.ActivityInfo  Build 
android.os  Debug 
android.os  Environment 
android.os  Handler 
android.os  Looper 
android.os  StatFs 
android.os  BOARD android.os.Build  BRAND android.os.Build  DEVICE android.os.Build  FINGERPRINT android.os.Build  HARDWARE android.os.Build  MANUFACTURER android.os.Build  MODEL android.os.Build  PRODUCT android.os.Build  SUPPORTED_32_BIT_ABIS android.os.Build  SUPPORTED_64_BIT_ABIS android.os.Build  SUPPORTED_ABIS android.os.Build  CODENAME android.os.Build.VERSION  INCREMENTAL android.os.Build.VERSION  RELEASE android.os.Build.VERSION  SDK_INT android.os.Build.VERSION  
JELLY_BEAN android.os.Build.VERSION_CODES  JELLY_BEAN_MR2 android.os.Build.VERSION_CODES  LOLLIPOP android.os.Build.VERSION_CODES  getNativeHeapAllocatedSize android.os.Debug  getNativeHeapFreeSize android.os.Debug  getNativeHeapSize android.os.Debug  
MEDIA_MOUNTED android.os.Environment  getDataDirectory android.os.Environment  getExternalStorageDirectory android.os.Environment  getExternalStorageState android.os.Environment  post android.os.Handler  postDelayed android.os.Handler  removeCallbacks android.os.Handler  
getMainLooper android.os.Looper  
blockCount android.os.StatFs  blockCountLong android.os.StatFs  	blockSize android.os.StatFs  
blockSizeLong android.os.StatFs  
freeBlocks android.os.StatFs  freeBlocksLong android.os.StatFs  
WindowManager android.view  ActivityInfo  android.view.ContextThemeWrapper  Build  android.view.ContextThemeWrapper  DeviceInfoPlugin  android.view.ContextThemeWrapper  
MethodChannel  android.view.ContextThemeWrapper  PerformancePlugin  android.view.ContextThemeWrapper  WebSettings  android.view.ContextThemeWrapper  
WindowManager  android.view.ContextThemeWrapper  addFlags android.view.Window  
clearFlags android.view.Window  FLAG_FULLSCREEN 'android.view.WindowManager.LayoutParams  FLAG_KEEP_SCREEN_ON 'android.view.WindowManager.LayoutParams  WebSettings android.webkit  getDefaultUserAgent android.webkit.WebSettings  ActivityInfo com.example.kft  ActivityManager com.example.kft  Any com.example.kft  Boolean com.example.kft  Build com.example.kft  Context com.example.kft  Debug com.example.kft  DeviceInfoPlugin com.example.kft  Environment com.example.kft  	Exception com.example.kft  File com.example.kft  FlutterActivity com.example.kft  
FlutterEngine com.example.kft  
FlutterPlugin com.example.kft  Handler com.example.kft  Int com.example.kft  Long com.example.kft  Looper com.example.kft  MainActivity com.example.kft  Map com.example.kft  
MethodCall com.example.kft  MethodCallHandler com.example.kft  
MethodChannel com.example.kft  Object com.example.kft  PerformancePlugin com.example.kft  RandomAccessFile com.example.kft  Regex com.example.kft  Result com.example.kft  Runnable com.example.kft  Runtime com.example.kft  StatFs com.example.kft  String com.example.kft  System com.example.kft  Thread com.example.kft  
WeakReference com.example.kft  WebSettings com.example.kft  
WindowManager com.example.kft  channel com.example.kft  	emptyList com.example.kft  getCurrentMemoryUsage com.example.kft  handler com.example.kft  isMonitoring com.example.kft  let com.example.kft  mapOf com.example.kft  mutableMapOf com.example.kft  optimizeMemory com.example.kft  println com.example.kft  readText com.example.kft  set com.example.kft  split com.example.kft  to com.example.kft  toDoubleOrNull com.example.kft  toList com.example.kft  toLong com.example.kft  toRegex com.example.kft  trim com.example.kft  ActivityManager  com.example.kft.DeviceInfoPlugin  Build  com.example.kft.DeviceInfoPlugin  Context  com.example.kft.DeviceInfoPlugin  Debug  com.example.kft.DeviceInfoPlugin  Environment  com.example.kft.DeviceInfoPlugin  File  com.example.kft.DeviceInfoPlugin  
MethodChannel  com.example.kft.DeviceInfoPlugin  RandomAccessFile  com.example.kft.DeviceInfoPlugin  Regex  com.example.kft.DeviceInfoPlugin  Runtime  com.example.kft.DeviceInfoPlugin  StatFs  com.example.kft.DeviceInfoPlugin  System  com.example.kft.DeviceInfoPlugin  channel  com.example.kft.DeviceInfoPlugin  context  com.example.kft.DeviceInfoPlugin  	emptyList  com.example.kft.DeviceInfoPlugin  
getCpuInfo  com.example.kft.DeviceInfoPlugin  
getDeviceInfo  com.example.kft.DeviceInfoPlugin  
getMemoryInfo  com.example.kft.DeviceInfoPlugin  getStorageInfo  com.example.kft.DeviceInfoPlugin  getStorageStats  com.example.kft.DeviceInfoPlugin  getTotalMemoryLegacy  com.example.kft.DeviceInfoPlugin  mapOf  com.example.kft.DeviceInfoPlugin  mutableMapOf  com.example.kft.DeviceInfoPlugin  readText  com.example.kft.DeviceInfoPlugin  set  com.example.kft.DeviceInfoPlugin  split  com.example.kft.DeviceInfoPlugin  to  com.example.kft.DeviceInfoPlugin  toDoubleOrNull  com.example.kft.DeviceInfoPlugin  toList  com.example.kft.DeviceInfoPlugin  toLong  com.example.kft.DeviceInfoPlugin  toRegex  com.example.kft.DeviceInfoPlugin  trim  com.example.kft.DeviceInfoPlugin  FlutterPluginBinding com.example.kft.FlutterPlugin  ActivityInfo com.example.kft.MainActivity  Build com.example.kft.MainActivity  CHANNEL com.example.kft.MainActivity  DeviceInfoPlugin com.example.kft.MainActivity  
MethodChannel com.example.kft.MainActivity  PerformancePlugin com.example.kft.MainActivity  WebSettings com.example.kft.MainActivity  
WindowManager com.example.kft.MainActivity  requestedOrientation com.example.kft.MainActivity  window com.example.kft.MainActivity  ActivityManager !com.example.kft.PerformancePlugin  Context !com.example.kft.PerformancePlugin  Debug !com.example.kft.PerformancePlugin  Handler !com.example.kft.PerformancePlugin  Looper !com.example.kft.PerformancePlugin  
MethodChannel !com.example.kft.PerformancePlugin  Object !com.example.kft.PerformancePlugin  Runtime !com.example.kft.PerformancePlugin  System !com.example.kft.PerformancePlugin  Thread !com.example.kft.PerformancePlugin  
WeakReference !com.example.kft.PerformancePlugin  calculateMemoryPressure !com.example.kft.PerformancePlugin  channel !com.example.kft.PerformancePlugin  clearWeakReferences !com.example.kft.PerformancePlugin  context !com.example.kft.PerformancePlugin  getCurrentMemoryUsage !com.example.kft.PerformancePlugin  getPerformanceMetrics !com.example.kft.PerformancePlugin  handler !com.example.kft.PerformancePlugin  isMonitoring !com.example.kft.PerformancePlugin  let !com.example.kft.PerformancePlugin  mapOf !com.example.kft.PerformancePlugin  memoryMonitoringRunnable !com.example.kft.PerformancePlugin  optimizeMemory !com.example.kft.PerformancePlugin  println !com.example.kft.PerformancePlugin  startMemoryMonitoring !com.example.kft.PerformancePlugin  stopMemoryMonitoring !com.example.kft.PerformancePlugin  to !com.example.kft.PerformancePlugin  triggerGarbageCollection !com.example.kft.PerformancePlugin  
trimMemory !com.example.kft.PerformancePlugin  FlutterActivity io.flutter.embedding.android  ActivityInfo ,io.flutter.embedding.android.FlutterActivity  Build ,io.flutter.embedding.android.FlutterActivity  DeviceInfoPlugin ,io.flutter.embedding.android.FlutterActivity  
MethodChannel ,io.flutter.embedding.android.FlutterActivity  PerformancePlugin ,io.flutter.embedding.android.FlutterActivity  WebSettings ,io.flutter.embedding.android.FlutterActivity  
WindowManager ,io.flutter.embedding.android.FlutterActivity  configureFlutterEngine ,io.flutter.embedding.android.FlutterActivity  
FlutterEngine io.flutter.embedding.engine  dartExecutor )io.flutter.embedding.engine.FlutterEngine  plugins )io.flutter.embedding.engine.FlutterEngine  DartExecutor  io.flutter.embedding.engine.dart  binaryMessenger -io.flutter.embedding.engine.dart.DartExecutor  
FlutterPlugin #io.flutter.embedding.engine.plugins  PluginRegistry #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  add 2io.flutter.embedding.engine.plugins.PluginRegistry  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  argument #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  invokeMethod &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  <SAM-CONSTRUCTOR> 8io.flutter.plugin.common.MethodChannel.MethodCallHandler  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  File java.io  RandomAccessFile java.io  exists java.io.File  path java.io.File  readText java.io.File  close java.io.RandomAccessFile  readLine java.io.RandomAccessFile  	Exception 	java.lang  Object 	java.lang  Runnable 	java.lang  message java.lang.Exception  let java.lang.Runnable  availableProcessors java.lang.Runtime  
freeMemory java.lang.Runtime  
getRuntime java.lang.Runtime  	maxMemory java.lang.Runtime  totalMemory java.lang.Runtime  currentTimeMillis java.lang.System  gc java.lang.System  getProperty java.lang.System  nanoTime java.lang.System  runFinalization java.lang.System  activeCount java.lang.Thread  sleep java.lang.Thread  
WeakReference 
java.lang.ref  CharSequence kotlin  	Function1 kotlin  	Function2 kotlin  Pair kotlin  let kotlin  to kotlin  toList kotlin  	compareTo 
kotlin.Double  div 
kotlin.Double  times 
kotlin.Double  toInt 
kotlin.Double  	compareTo 
kotlin.Int  times 
kotlin.Int  toLong 
kotlin.Int  div kotlin.Long  minus kotlin.Long  times kotlin.Long  toDouble kotlin.Long  split 
kotlin.String  to 
kotlin.String  toDoubleOrNull 
kotlin.String  toLong 
kotlin.String  toRegex 
kotlin.String  trim 
kotlin.String  message kotlin.Throwable  List kotlin.collections  Map kotlin.collections  
MutableMap kotlin.collections  	emptyList kotlin.collections  mapOf kotlin.collections  mutableMapOf kotlin.collections  set kotlin.collections  toList kotlin.collections  get kotlin.collections.List  size kotlin.collections.List  get kotlin.collections.Map  set kotlin.collections.MutableMap  println 	kotlin.io  readText 	kotlin.io  toList kotlin.sequences  MatchResult kotlin.text  Regex kotlin.text  set kotlin.text  split kotlin.text  toDoubleOrNull kotlin.text  toList kotlin.text  toLong kotlin.text  toRegex kotlin.text  trim kotlin.text  groupValues kotlin.text.MatchResult  find kotlin.text.Regex                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            