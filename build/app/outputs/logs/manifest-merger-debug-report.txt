-- Merging decision tree log ---
application
INJECTED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:19:5-51:19
INJECTED from /Users/<USER>/Desktop/59/android/app/src/debug/AndroidManifest.xml
MERGED from [:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-33:19
MERGED from [:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-33:19
MERGED from [:firebase_analytics] /Users/<USER>/Desktop/59/build/firebase_analytics/intermediates/merged_manifest/debug/AndroidManifest.xml:11:5-17:19
MERGED from [:firebase_analytics] /Users/<USER>/Desktop/59/build/firebase_analytics/intermediates/merged_manifest/debug/AndroidManifest.xml:11:5-17:19
MERGED from [:firebase_core] /Users/<USER>/Desktop/59/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] /Users/<USER>/Desktop/59/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-13:19
MERGED from [com.google.firebase:firebase-analytics-ktx:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/c8b8cf0be5b39564d368c9c946066111/transformed/firebase-analytics-ktx-22.4.0/AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics-ktx:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/c8b8cf0be5b39564d368c9c946066111/transformed/firebase-analytics-ktx-22.4.0/AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-messaging-ktx:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/72e57fc24a13f1cb41009954b9a12311/transformed/firebase-messaging-ktx-24.1.1/AndroidManifest.xml:22:5-30:19
MERGED from [com.google.firebase:firebase-messaging-ktx:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/72e57fc24a13f1cb41009954b9a12311/transformed/firebase-messaging-ktx-24.1.1/AndroidManifest.xml:22:5-30:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-analytics:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/a442f6865dd2ab2f88c4cdbb3d6c1e16/transformed/firebase-analytics-22.4.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/a442f6865dd2ab2f88c4cdbb3d6c1e16/transformed/firebase-analytics-22.4.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:29:5-37:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/e9fa5516e1bccc9a746fa45b1bb2aaf9/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/e9fa5516e1bccc9a746fa45b1bb2aaf9/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:22:5-39:19
MERGED from [:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-32:19
MERGED from [:url_launcher_android] /Users/<USER>/Desktop/59/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] /Users/<USER>/Desktop/59/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-12:19
MERGED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-45:19
MERGED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-45:19
MERGED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:13:5-46:19
MERGED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:13:5-46:19
MERGED from [me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:14:5-29:19
MERGED from [me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:14:5-29:19
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/transforms-3/467a3199382fb71113479a03289e69bb/transformed/material-1.12.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/transforms-3/467a3199382fb71113479a03289e69bb/transformed/material-1.12.0/AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/transforms-3/24be8370b4379e907e49b02cddd1183c/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/transforms-3/24be8370b4379e907e49b02cddd1183c/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/0eb9e273a1b2a9288228e1c0f3a26ae5/transformed/play-services-measurement-sdk-22.4.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/0eb9e273a1b2a9288228e1c0f3a26ae5/transformed/play-services-measurement-sdk-22.4.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/transforms-3/30906978f976bd6262b01554e4dc71ef/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/transforms-3/30906978f976bd6262b01554e4dc71ef/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/transforms-3/7b94bd3fd45f2ee88f5ffd9a41852727/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/transforms-3/7b94bd3fd45f2ee88f5ffd9a41852727/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/b572e6391ee6f610a1ac5fb044e145f0/transformed/play-services-measurement-impl-22.4.0/AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/b572e6391ee6f610a1ac5fb044e145f0/transformed/play-services-measurement-impl-22.4.0/AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/transforms-3/9d9da933e8dc1837a3f2ec86e41240dc/transformed/play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/transforms-3/9d9da933e8dc1837a3f2ec86e41240dc/transformed/play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/3903fc12867b744337a3d399dce716f0/transformed/play-services-ads-identifier-18.0.0/AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/3903fc12867b744337a3d399dce716f0/transformed/play-services-ads-identifier-18.0.0/AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/0663566f035547712eb9b8b2494af47f/transformed/play-services-measurement-base-22.4.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/0663566f035547712eb9b8b2494af47f/transformed/play-services-measurement-base-22.4.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/ce98a87c43f97d057c48b3433fc10c79/transformed/play-services-base-18.5.0/AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/ce98a87c43f97d057c48b3433fc10c79/transformed/play-services-base-18.5.0/AndroidManifest.xml:4:5-6:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/16693b5e1a09ace5ff4ca3456cfe7953/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/16693b5e1a09ace5ff4ca3456cfe7953/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/066ad0703a4a4a7e35fddd8e645492a3/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/066ad0703a4a4a7e35fddd8e645492a3/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b21b8ff5629daa6ae82a2981003c3e1/transformed/window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b21b8ff5629daa6ae82a2981003c3e1/transformed/window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/transforms-3/1f7636bc79bcca6cfc240d5569ab97dd/transformed/ads-adservices-1.1.0-beta11/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/transforms-3/1f7636bc79bcca6cfc240d5569ab97dd/transformed/ads-adservices-1.1.0-beta11/AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/b63904aecfed9d0a8fd4c495383605fd/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/b63904aecfed9d0a8fd4c495383605fd/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/7178fedea55c950c8017bbfb028a9590/transformed/play-services-basement-18.5.0/AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/7178fedea55c950c8017bbfb028a9590/transformed/play-services-basement-18.5.0/AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/48b2b5e224a2f110a57147aa68b2a2c6/transformed/room-runtime-2.6.1/AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/48b2b5e224a2f110a57147aa68b2a2c6/transformed/room-runtime-2.6.1/AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/a7a8e61d0ac5ab0e055aa6cb087c14e4/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/a7a8e61d0ac5ab0e055aa6cb087c14e4/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/d2a074df3f50ee54bbde0b09e0de1067/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/d2a074df3f50ee54bbde0b09e0de1067/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/d8217dc7863bca0dfd259441afb86ea5/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/d8217dc7863bca0dfd259441afb86ea5/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:25:5-39:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Desktop/59/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
	android:name
		INJECTED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml
manifest
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:1:1-78:12
MERGED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:1:1-78:12
INJECTED from /Users/<USER>/Desktop/59/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Desktop/59/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Desktop/59/android/app/src/debug/AndroidManifest.xml:1:1-7:12
MERGED from [:app_settings] /Users/<USER>/Desktop/59/build/app_settings/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:device_info_plus] /Users/<USER>/Desktop/59/build/device_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:wakelock_plus] /Users/<USER>/Desktop/59/build/wakelock_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:package_info_plus] /Users/<USER>/Desktop/59/build/package_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-35:12
MERGED from [:shared_preferences_android] /Users/<USER>/Desktop/59/build/shared_preferences_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:webview_flutter_android] /Users/<USER>/Desktop/59/build/webview_flutter_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:firebase_analytics] /Users/<USER>/Desktop/59/build/firebase_analytics/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-19:12
MERGED from [:firebase_core] /Users/<USER>/Desktop/59/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-15:12
MERGED from [com.google.firebase:firebase-analytics-ktx:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/c8b8cf0be5b39564d368c9c946066111/transformed/firebase-analytics-ktx-22.4.0/AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-messaging-ktx:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/72e57fc24a13f1cb41009954b9a12311/transformed/firebase-messaging-ktx-24.1.1/AndroidManifest.xml:17:1-32:12
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-analytics:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/a442f6865dd2ab2f88c4cdbb3d6c1e16/transformed/firebase-analytics-22.4.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:17:1-39:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/e9fa5516e1bccc9a746fa45b1bb2aaf9/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:15:1-41:12
MERGED from [:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-34:12
MERGED from [:url_launcher_android] /Users/<USER>/Desktop/59/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-14:12
MERGED from [:android_intent_plus] /Users/<USER>/Desktop/59/build/android_intent_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-47:12
MERGED from [:connectivity_plus] /Users/<USER>/Desktop/59/build/connectivity_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-9:12
MERGED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-48:12
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/Desktop/59/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_secure_storage] /Users/<USER>/Desktop/59/build/flutter_secure_storage/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-10:12
MERGED from [:path_provider_android] /Users/<USER>/Desktop/59/build/path_provider_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] /Users/<USER>/Desktop/59/build/permission_handler_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:screen_protector] /Users/<USER>/Desktop/59/build/screen_protector/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] /Users/<USER>/Desktop/59/build/sqflite_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:video_player_android] /Users/<USER>/Desktop/59/build/video_player_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/3d6f0d49e83e33b0ea2df0ebd229f797/transformed/browser-1.8.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/c3bf30575fd31b9c1b9362cb5124bbe4/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:2:1-31:12
MERGED from [androidx.preference:preference:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/76d2e61486788019e765d06df0a9d1c7/transformed/preference-1.2.1/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/transforms-3/467a3199382fb71113479a03289e69bb/transformed/material-1.12.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/ad98411b5ad2c07798139d167a53331b/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/transforms-3/24be8370b4379e907e49b02cddd1183c/transformed/constraintlayout-2.0.1/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/14eb9eff792770dbb80324f3dd5a4ec0/transformed/appcompat-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] /Users/<USER>/.gradle/caches/transforms-3/bc3e751540d72e7206f2c240e36ebe2c/transformed/fragment-ktx-1.7.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/52dc3bc3656cb90f2e5c31d39aae5e46/transformed/viewpager2-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/0eb9e273a1b2a9288228e1c0f3a26ae5/transformed/play-services-measurement-sdk-22.4.0/AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/transforms-3/30906978f976bd6262b01554e4dc71ef/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/transforms-3/7b94bd3fd45f2ee88f5ffd9a41852727/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/f493bdc918884138078b7373670d0c67/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/b572e6391ee6f610a1ac5fb044e145f0/transformed/play-services-measurement-impl-22.4.0/AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/transforms-3/9d9da933e8dc1837a3f2ec86e41240dc/transformed/play-services-stats-17.0.2/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/3903fc12867b744337a3d399dce716f0/transformed/play-services-ads-identifier-18.0.0/AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/a4790809080a1312f4efb82681ff771f/transformed/play-services-measurement-sdk-api-22.4.0/AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/0663566f035547712eb9b8b2494af47f/transformed/play-services-measurement-base-22.4.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/transforms-3/1426c0ed4115678231a37ce22b420045/transformed/firebase-installations-interop-17.1.1/AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/ce98a87c43f97d057c48b3433fc10c79/transformed/play-services-base-18.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.3] /Users/<USER>/.gradle/caches/transforms-3/ba5e90de550120c6c29079ca342c58db/transformed/activity-ktx-1.9.3/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/4b42a6bb97ad93459aeca973f0f0f55e/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b330c0ccf3b73664bc475bc6a2ecad5a/transformed/transition-1.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/b87227b02003925fc649bc28bccb26fc/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/266d4d21a6c1df30fb422b1a73e75176/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/4a453a30e902c92431724a0ab5fe36ed/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/915770f0c6f5662933bca3b38e40d9db/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/6d47c292a84aa741355c23b57d50ddd6/transformed/lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/4f488121dfb4c6db2c9f373ef1239f17/transformed/lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/b15e83eaa90a5a1a43ee3339928d19cd/transformed/lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/d155f80263f43a7288b12f78421f92f8/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/0d2d6b635765aae7af8bdd0b725fbf57/transformed/lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/3189a20ce2937e4ad2a2c0be7f3d4c6b/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/e1b997ea2da97acd8543f9995d2f4957/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/a34a83057ea127f8df75a01c8c8a0b46/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/16693b5e1a09ace5ff4ca3456cfe7953/transformed/emoji2-1.3.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/066ad0703a4a4a7e35fddd8e645492a3/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/07a4b8a28f3b0c753ca248a39a226274/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/0fa9bc69cc91a3fceb1f37075e9803f7/transformed/savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/493d21a048c0b85afa96764c82761220/transformed/window-java-1.2.0/AndroidManifest.xml:17:1-21:12
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b21b8ff5629daa6ae82a2981003c3e1/transformed/window-1.2.0/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] /Users/<USER>/.gradle/caches/transforms-3/a2b091df28ac82aa9492261977b37272/transformed/datastore-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] /Users/<USER>/.gradle/caches/transforms-3/e9ce7f77a0b21eff4ba649a9818ee50c/transformed/datastore-preferences-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] /Users/<USER>/.gradle/caches/transforms-3/dd5bec377580e0b7b48a8a1859bf460e/transformed/datastore-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] /Users/<USER>/.gradle/caches/transforms-3/8f01f6c3130c7847038452a5ed587f2f/transformed/ads-adservices-java-1.1.0-beta11/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/transforms-3/1f7636bc79bcca6cfc240d5569ab97dd/transformed/ads-adservices-1.1.0-beta11/AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/b63904aecfed9d0a8fd4c495383605fd/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/7178fedea55c950c8017bbfb028a9590/transformed/play-services-basement-18.5.0/AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/transforms-3/36494e92349f734744ccde87c40d8ff0/transformed/fragment-1.7.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] /Users/<USER>/.gradle/caches/transforms-3/05b14d40488a87313b1d518221092c9f/transformed/activity-1.9.3/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/842d29af1d335def8f2c52a86b671b27/transformed/media3-extractor-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/a695d8ba24a9caf74975b842c1b1333d/transformed/media3-container-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/70b46f9c0b45310ed4d8abab7fb56baa/transformed/media3-datasource-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/b30885acf4b2979ceec35bf23536bce5/transformed/media3-decoder-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/576bbad1114e2ce38dcab2ab34182a6e/transformed/media3-database-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/51c855c05cda79b2e3daea3b6f815359/transformed/media3-common-1.4.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/e1628c0976d33621ff33b8cecc405ff8/transformed/media3-exoplayer-hls-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/12d2b6116ece412466a7dbbef132e0d9/transformed/media3-exoplayer-dash-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/fcc9720011876026da4a07bc7130bbd3/transformed/media3-exoplayer-rtsp-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/ced86e8f7c0cd24537ab9b470ab306c1/transformed/media3-exoplayer-smoothstreaming-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/e0b353b8a8ecb8c8f094a722df15c93c/transformed/media3-exoplayer-1.4.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.12.1] /Users/<USER>/.gradle/caches/transforms-3/3dda310a8ed85dc3ca21fb8b0fffc6d4/transformed/webkit-1.12.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/e1432422334461c5abf599b8dbddd270/transformed/media-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/04cabad0d9956a25f5c60d22b5bd2b74/transformed/core-ktx-1.13.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/ba1305210d7b1c0f3520def841e55ed5/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/7229cf67bf0c344836cb161dd3a335cd/transformed/drawerlayout-1.1.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/573cde57e17d30d829a1457e03438b3b/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/7c7209f139f051b6f880ed68f5a57e51/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/7958b33ddf963dab1b5dcc9d2ead36f9/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/f33cf5ea82bedd4794279fb2563bbcfd/transformed/recyclerview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/7c6e544787d5ec564aa20635c64443cb/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/48b2b5e224a2f110a57147aa68b2a2c6/transformed/room-runtime-2.6.1/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] /Users/<USER>/.gradle/caches/transforms-3/3baaf18cfc31dee895f958d100ebe0dc/transformed/sqlite-framework-2.4.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] /Users/<USER>/.gradle/caches/transforms-3/f3aee0677b7e05257474d20122f2bb0e/transformed/sqlite-2.4.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] /Users/<USER>/.gradle/caches/transforms-3/3f012daa273a3805a86fce503ea546a1/transformed/security-crypto-1.1.0-alpha06/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] /Users/<USER>/.gradle/caches/transforms-3/fdbc2740bba006d61e7aab42317d51d6/transformed/exifinterface-1.3.7/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/dbb2724e4d71d30ed9011fddbb54a8a2/transformed/firebase-components-18.0.0/AndroidManifest.xml:15:1-20:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/a7a8e61d0ac5ab0e055aa6cb087c14e4/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/a513521685cc88bfc2f102ef1bd90193/transformed/tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/95b907667c7473273b1264d0b8c0fdee/transformed/localbroadcastmanager-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/35d396fc589e5d0c8f26e60dad7a1ae6/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/d2a074df3f50ee54bbde0b09e0de1067/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/d8217dc7863bca0dfd259441afb86ea5/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/709a26a8a6ca23234da41827a47e3a52/transformed/firebase-encoders-json-18.0.0/AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/transforms-3/2c94fb9cbc8b6ff89f502efe195a5918/transformed/transport-api-3.1.0/AndroidManifest.xml:15:1-20:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/5626ace690c7419151e0238d162f02e3/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a52f06f50a7cb96f36baa4ddcc0a8983/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/04f10413b4ea7b9417c6def6ec841244/transformed/cardview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f33e7f7b42507cae78e6d9a0a6a403c0/transformed/core-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f41057d02a4a56c6252621dbb0e21c14/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a209b2181c5c40a4c035c4b3d9726a22/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/209c62e829ddce36887d6d5e265a2954/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.multidex:multidex:2.0.1] /Users/<USER>/.gradle/caches/transforms-3/e476e707926cd306439bf40c0718da39/transformed/multidex-2.0.1/AndroidManifest.xml:17:1-22:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/transforms-3/8d14721d707f116d0540e055bec12a96/transformed/relinker-1.4.5/AndroidManifest.xml:2:1-7:12
MERGED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:2:1-52:12
	package
		ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:1:70-95
		INJECTED from /Users/<USER>/Desktop/59/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Desktop/59/android/app/src/debug/AndroidManifest.xml
	android:versionCode
		INJECTED from /Users/<USER>/Desktop/59/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:2:5-66
MERGED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:2:5-66
MERGED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:2:5-66
MERGED from [:firebase_analytics] /Users/<USER>/Desktop/59/build/firebase_analytics/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-67
MERGED from [:firebase_analytics] /Users/<USER>/Desktop/59/build/firebase_analytics/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/f493bdc918884138078b7373670d0c67/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/f493bdc918884138078b7373670d0c67/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/b572e6391ee6f610a1ac5fb044e145f0/transformed/play-services-measurement-impl-22.4.0/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/b572e6391ee6f610a1ac5fb044e145f0/transformed/play-services-measurement-impl-22.4.0/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/a4790809080a1312f4efb82681ff771f/transformed/play-services-measurement-sdk-api-22.4.0/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/a4790809080a1312f4efb82681ff771f/transformed/play-services-measurement-sdk-api-22.4.0/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:25:5-67
	android:name
		ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:2:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:3:5-78
MERGED from [:firebase_analytics] /Users/<USER>/Desktop/59/build/firebase_analytics/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-79
MERGED from [:firebase_analytics] /Users/<USER>/Desktop/59/build/firebase_analytics/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:7:5-79
MERGED from [:connectivity_plus] /Users/<USER>/Desktop/59/build/connectivity_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-79
MERGED from [:connectivity_plus] /Users/<USER>/Desktop/59/build/connectivity_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/f493bdc918884138078b7373670d0c67/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/f493bdc918884138078b7373670d0c67/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/b572e6391ee6f610a1ac5fb044e145f0/transformed/play-services-measurement-impl-22.4.0/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/b572e6391ee6f610a1ac5fb044e145f0/transformed/play-services-measurement-impl-22.4.0/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/a4790809080a1312f4efb82681ff771f/transformed/play-services-measurement-sdk-api-22.4.0/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/a4790809080a1312f4efb82681ff771f/transformed/play-services-measurement-sdk-api-22.4.0/AndroidManifest.xml:24:5-79
MERGED from [androidx.media3:media3-common:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/51c855c05cda79b2e3daea3b6f815359/transformed/media3-common-1.4.1/AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/51c855c05cda79b2e3daea3b6f815359/transformed/media3-common-1.4.1/AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/e0b353b8a8ecb8c8f094a722df15c93c/transformed/media3-exoplayer-1.4.1/AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/e0b353b8a8ecb8c8f094a722df15c93c/transformed/media3-exoplayer-1.4.1/AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:22:5-79
	android:name
		ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:3:22-76
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:4:5-76
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:23:5-77
MERGED from [me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:8:5-77
MERGED from [me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:8:5-77
	android:name
		ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:4:22-74
uses-permission#android.permission.WAKE_LOCK
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:5:5-67
MERGED from [:firebase_analytics] /Users/<USER>/Desktop/59/build/firebase_analytics/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-68
MERGED from [:firebase_analytics] /Users/<USER>/Desktop/59/build/firebase_analytics/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/f493bdc918884138078b7373670d0c67/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/f493bdc918884138078b7373670d0c67/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/b572e6391ee6f610a1ac5fb044e145f0/transformed/play-services-measurement-impl-22.4.0/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/b572e6391ee6f610a1ac5fb044e145f0/transformed/play-services-measurement-impl-22.4.0/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/a4790809080a1312f4efb82681ff771f/transformed/play-services-measurement-sdk-api-22.4.0/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/a4790809080a1312f4efb82681ff771f/transformed/play-services-measurement-sdk-api-22.4.0/AndroidManifest.xml:25:5-68
	android:name
		ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:5:22-65
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:6:5-80
	android:name
		ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:6:22-78
uses-permission#android.permission.VIBRATE
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:7:5-65
	android:name
		ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:7:22-63
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:8:5-78
	android:name
		ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:8:22-76
uses-permission#android.permission.USE_EXACT_ALARM
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:9:5-73
	android:name
		ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:9:22-71
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:10:5-76
	android:name
		ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:10:22-74
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:11:5-94
	android:name
		ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:11:22-92
uses-permission#android.permission.USE_FULL_SCREEN_INTENT
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:14:5-81
	android:name
		ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:14:22-78
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:15:5-78
	android:name
		ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:15:22-75
uses-permission#android.permission.FOREGROUND_SERVICE_SPECIAL_USE
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:16:5-89
	android:name
		ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:16:22-86
uses-permission#android.permission.DISABLE_KEYGUARD
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:17:5-75
	android:name
		ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:17:22-72
uses-permission#android.permission.TURN_SCREEN_ON
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:18:5-73
	android:name
		ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:18:22-70
queries
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:57:5-77:15
MERGED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-11:15
MERGED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-11:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:58:9-61:18
action#android.intent.action.PROCESS_TEXT
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:59:13-72
	android:name
		ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:59:21-70
data
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:60:13-50
	android:mimeType
		ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:60:19-48
package#com.whatsapp
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:63:9-48
	android:name
		ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:63:18-45
intent#action:name:android.intent.action.VIEW+data:scheme:https
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:65:9-68:18
action#android.intent.action.VIEW
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:66:13-65
	android:name
		ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:66:21-62
intent#action:name:android.intent.action.VIEW+data:scheme:http
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:69:9-72:18
intent#action:name:android.intent.action.VIEW+data:scheme:whatsapp
ADDED from /Users/<USER>/Desktop/59/android/app/src/main/AndroidManifest.xml:73:9-76:18
uses-sdk
INJECTED from /Users/<USER>/Desktop/59/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Desktop/59/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/Desktop/59/android/app/src/debug/AndroidManifest.xml
MERGED from [:app_settings] /Users/<USER>/Desktop/59/build/app_settings/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:app_settings] /Users/<USER>/Desktop/59/build/app_settings/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] /Users/<USER>/Desktop/59/build/device_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] /Users/<USER>/Desktop/59/build/device_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:wakelock_plus] /Users/<USER>/Desktop/59/build/wakelock_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:wakelock_plus] /Users/<USER>/Desktop/59/build/wakelock_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] /Users/<USER>/Desktop/59/build/package_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] /Users/<USER>/Desktop/59/build/package_info_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] /Users/<USER>/Desktop/59/build/shared_preferences_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] /Users/<USER>/Desktop/59/build/shared_preferences_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] /Users/<USER>/Desktop/59/build/webview_flutter_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] /Users/<USER>/Desktop/59/build/webview_flutter_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:firebase_analytics] /Users/<USER>/Desktop/59/build/firebase_analytics/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:firebase_analytics] /Users/<USER>/Desktop/59/build/firebase_analytics/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] /Users/<USER>/Desktop/59/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] /Users/<USER>/Desktop/59/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/c8b8cf0be5b39564d368c9c946066111/transformed/firebase-analytics-ktx-22.4.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/c8b8cf0be5b39564d368c9c946066111/transformed/firebase-analytics-ktx-22.4.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging-ktx:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/72e57fc24a13f1cb41009954b9a12311/transformed/firebase-messaging-ktx-24.1.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging-ktx:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/72e57fc24a13f1cb41009954b9a12311/transformed/firebase-messaging-ktx-24.1.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-analytics:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/a442f6865dd2ab2f88c4cdbb3d6c1e16/transformed/firebase-analytics-22.4.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/a442f6865dd2ab2f88c4cdbb3d6c1e16/transformed/firebase-analytics-22.4.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/e9fa5516e1bccc9a746fa45b1bb2aaf9/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/e9fa5516e1bccc9a746fa45b1bb2aaf9/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:19:5-44
MERGED from [:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:6:5-44
MERGED from [:url_launcher_android] /Users/<USER>/Desktop/59/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] /Users/<USER>/Desktop/59/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:android_intent_plus] /Users/<USER>/Desktop/59/build/android_intent_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:android_intent_plus] /Users/<USER>/Desktop/59/build/android_intent_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] /Users/<USER>/Desktop/59/build/connectivity_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] /Users/<USER>/Desktop/59/build/connectivity_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/Desktop/59/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/Desktop/59/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:flutter_secure_storage] /Users/<USER>/Desktop/59/build/flutter_secure_storage/intermediates/merged_manifest/debug/AndroidManifest.xml:6:5-8:53
MERGED from [:flutter_secure_storage] /Users/<USER>/Desktop/59/build/flutter_secure_storage/intermediates/merged_manifest/debug/AndroidManifest.xml:6:5-8:53
MERGED from [:path_provider_android] /Users/<USER>/Desktop/59/build/path_provider_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/Desktop/59/build/path_provider_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] /Users/<USER>/Desktop/59/build/permission_handler_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] /Users/<USER>/Desktop/59/build/permission_handler_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:screen_protector] /Users/<USER>/Desktop/59/build/screen_protector/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:screen_protector] /Users/<USER>/Desktop/59/build/screen_protector/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] /Users/<USER>/Desktop/59/build/sqflite_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] /Users/<USER>/Desktop/59/build/sqflite_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] /Users/<USER>/Desktop/59/build/video_player_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] /Users/<USER>/Desktop/59/build/video_player_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/3d6f0d49e83e33b0ea2df0ebd229f797/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/3d6f0d49e83e33b0ea2df0ebd229f797/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/c3bf30575fd31b9c1b9362cb5124bbe4/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/c3bf30575fd31b9c1b9362cb5124bbe4/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:6:5-44
MERGED from [me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:6:5-44
MERGED from [androidx.preference:preference:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/76d2e61486788019e765d06df0a9d1c7/transformed/preference-1.2.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/76d2e61486788019e765d06df0a9d1c7/transformed/preference-1.2.1/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/transforms-3/467a3199382fb71113479a03289e69bb/transformed/material-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/transforms-3/467a3199382fb71113479a03289e69bb/transformed/material-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/ad98411b5ad2c07798139d167a53331b/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/ad98411b5ad2c07798139d167a53331b/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/transforms-3/24be8370b4379e907e49b02cddd1183c/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/transforms-3/24be8370b4379e907e49b02cddd1183c/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/14eb9eff792770dbb80324f3dd5a4ec0/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/14eb9eff792770dbb80324f3dd5a4ec0/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] /Users/<USER>/.gradle/caches/transforms-3/bc3e751540d72e7206f2c240e36ebe2c/transformed/fragment-ktx-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] /Users/<USER>/.gradle/caches/transforms-3/bc3e751540d72e7206f2c240e36ebe2c/transformed/fragment-ktx-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/52dc3bc3656cb90f2e5c31d39aae5e46/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/52dc3bc3656cb90f2e5c31d39aae5e46/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/0eb9e273a1b2a9288228e1c0f3a26ae5/transformed/play-services-measurement-sdk-22.4.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/0eb9e273a1b2a9288228e1c0f3a26ae5/transformed/play-services-measurement-sdk-22.4.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/transforms-3/30906978f976bd6262b01554e4dc71ef/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/transforms-3/30906978f976bd6262b01554e4dc71ef/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/transforms-3/7b94bd3fd45f2ee88f5ffd9a41852727/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/transforms-3/7b94bd3fd45f2ee88f5ffd9a41852727/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/f493bdc918884138078b7373670d0c67/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/f493bdc918884138078b7373670d0c67/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/b572e6391ee6f610a1ac5fb044e145f0/transformed/play-services-measurement-impl-22.4.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/b572e6391ee6f610a1ac5fb044e145f0/transformed/play-services-measurement-impl-22.4.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/transforms-3/9d9da933e8dc1837a3f2ec86e41240dc/transformed/play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/transforms-3/9d9da933e8dc1837a3f2ec86e41240dc/transformed/play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/3903fc12867b744337a3d399dce716f0/transformed/play-services-ads-identifier-18.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/3903fc12867b744337a3d399dce716f0/transformed/play-services-ads-identifier-18.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/a4790809080a1312f4efb82681ff771f/transformed/play-services-measurement-sdk-api-22.4.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/a4790809080a1312f4efb82681ff771f/transformed/play-services-measurement-sdk-api-22.4.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/0663566f035547712eb9b8b2494af47f/transformed/play-services-measurement-base-22.4.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/0663566f035547712eb9b8b2494af47f/transformed/play-services-measurement-base-22.4.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/transforms-3/1426c0ed4115678231a37ce22b420045/transformed/firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/transforms-3/1426c0ed4115678231a37ce22b420045/transformed/firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/ce98a87c43f97d057c48b3433fc10c79/transformed/play-services-base-18.5.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/ce98a87c43f97d057c48b3433fc10c79/transformed/play-services-base-18.5.0/AndroidManifest.xml:3:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] /Users/<USER>/.gradle/caches/transforms-3/ba5e90de550120c6c29079ca342c58db/transformed/activity-ktx-1.9.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] /Users/<USER>/.gradle/caches/transforms-3/ba5e90de550120c6c29079ca342c58db/transformed/activity-ktx-1.9.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/4b42a6bb97ad93459aeca973f0f0f55e/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/4b42a6bb97ad93459aeca973f0f0f55e/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b330c0ccf3b73664bc475bc6a2ecad5a/transformed/transition-1.5.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b330c0ccf3b73664bc475bc6a2ecad5a/transformed/transition-1.5.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/b87227b02003925fc649bc28bccb26fc/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/b87227b02003925fc649bc28bccb26fc/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/266d4d21a6c1df30fb422b1a73e75176/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/266d4d21a6c1df30fb422b1a73e75176/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/4a453a30e902c92431724a0ab5fe36ed/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/4a453a30e902c92431724a0ab5fe36ed/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/915770f0c6f5662933bca3b38e40d9db/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/915770f0c6f5662933bca3b38e40d9db/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/6d47c292a84aa741355c23b57d50ddd6/transformed/lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/6d47c292a84aa741355c23b57d50ddd6/transformed/lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/4f488121dfb4c6db2c9f373ef1239f17/transformed/lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/4f488121dfb4c6db2c9f373ef1239f17/transformed/lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/b15e83eaa90a5a1a43ee3339928d19cd/transformed/lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/b15e83eaa90a5a1a43ee3339928d19cd/transformed/lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/d155f80263f43a7288b12f78421f92f8/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/d155f80263f43a7288b12f78421f92f8/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/0d2d6b635765aae7af8bdd0b725fbf57/transformed/lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/0d2d6b635765aae7af8bdd0b725fbf57/transformed/lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/3189a20ce2937e4ad2a2c0be7f3d4c6b/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/3189a20ce2937e4ad2a2c0be7f3d4c6b/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/e1b997ea2da97acd8543f9995d2f4957/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/e1b997ea2da97acd8543f9995d2f4957/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/a34a83057ea127f8df75a01c8c8a0b46/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/a34a83057ea127f8df75a01c8c8a0b46/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/16693b5e1a09ace5ff4ca3456cfe7953/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/16693b5e1a09ace5ff4ca3456cfe7953/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/066ad0703a4a4a7e35fddd8e645492a3/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/066ad0703a4a4a7e35fddd8e645492a3/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/07a4b8a28f3b0c753ca248a39a226274/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/07a4b8a28f3b0c753ca248a39a226274/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/0fa9bc69cc91a3fceb1f37075e9803f7/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/0fa9bc69cc91a3fceb1f37075e9803f7/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/493d21a048c0b85afa96764c82761220/transformed/window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/493d21a048c0b85afa96764c82761220/transformed/window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b21b8ff5629daa6ae82a2981003c3e1/transformed/window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b21b8ff5629daa6ae82a2981003c3e1/transformed/window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] /Users/<USER>/.gradle/caches/transforms-3/a2b091df28ac82aa9492261977b37272/transformed/datastore-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] /Users/<USER>/.gradle/caches/transforms-3/a2b091df28ac82aa9492261977b37272/transformed/datastore-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] /Users/<USER>/.gradle/caches/transforms-3/e9ce7f77a0b21eff4ba649a9818ee50c/transformed/datastore-preferences-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] /Users/<USER>/.gradle/caches/transforms-3/e9ce7f77a0b21eff4ba649a9818ee50c/transformed/datastore-preferences-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] /Users/<USER>/.gradle/caches/transforms-3/dd5bec377580e0b7b48a8a1859bf460e/transformed/datastore-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] /Users/<USER>/.gradle/caches/transforms-3/dd5bec377580e0b7b48a8a1859bf460e/transformed/datastore-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] /Users/<USER>/.gradle/caches/transforms-3/8f01f6c3130c7847038452a5ed587f2f/transformed/ads-adservices-java-1.1.0-beta11/AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] /Users/<USER>/.gradle/caches/transforms-3/8f01f6c3130c7847038452a5ed587f2f/transformed/ads-adservices-java-1.1.0-beta11/AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/transforms-3/1f7636bc79bcca6cfc240d5569ab97dd/transformed/ads-adservices-1.1.0-beta11/AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/transforms-3/1f7636bc79bcca6cfc240d5569ab97dd/transformed/ads-adservices-1.1.0-beta11/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/b63904aecfed9d0a8fd4c495383605fd/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/b63904aecfed9d0a8fd4c495383605fd/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/7178fedea55c950c8017bbfb028a9590/transformed/play-services-basement-18.5.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/7178fedea55c950c8017bbfb028a9590/transformed/play-services-basement-18.5.0/AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/transforms-3/36494e92349f734744ccde87c40d8ff0/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/transforms-3/36494e92349f734744ccde87c40d8ff0/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] /Users/<USER>/.gradle/caches/transforms-3/05b14d40488a87313b1d518221092c9f/transformed/activity-1.9.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] /Users/<USER>/.gradle/caches/transforms-3/05b14d40488a87313b1d518221092c9f/transformed/activity-1.9.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/842d29af1d335def8f2c52a86b671b27/transformed/media3-extractor-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/842d29af1d335def8f2c52a86b671b27/transformed/media3-extractor-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/a695d8ba24a9caf74975b842c1b1333d/transformed/media3-container-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/a695d8ba24a9caf74975b842c1b1333d/transformed/media3-container-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/70b46f9c0b45310ed4d8abab7fb56baa/transformed/media3-datasource-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/70b46f9c0b45310ed4d8abab7fb56baa/transformed/media3-datasource-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/b30885acf4b2979ceec35bf23536bce5/transformed/media3-decoder-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/b30885acf4b2979ceec35bf23536bce5/transformed/media3-decoder-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/576bbad1114e2ce38dcab2ab34182a6e/transformed/media3-database-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/576bbad1114e2ce38dcab2ab34182a6e/transformed/media3-database-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/51c855c05cda79b2e3daea3b6f815359/transformed/media3-common-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/51c855c05cda79b2e3daea3b6f815359/transformed/media3-common-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/e1628c0976d33621ff33b8cecc405ff8/transformed/media3-exoplayer-hls-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/e1628c0976d33621ff33b8cecc405ff8/transformed/media3-exoplayer-hls-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/12d2b6116ece412466a7dbbef132e0d9/transformed/media3-exoplayer-dash-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/12d2b6116ece412466a7dbbef132e0d9/transformed/media3-exoplayer-dash-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/fcc9720011876026da4a07bc7130bbd3/transformed/media3-exoplayer-rtsp-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/fcc9720011876026da4a07bc7130bbd3/transformed/media3-exoplayer-rtsp-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/ced86e8f7c0cd24537ab9b470ab306c1/transformed/media3-exoplayer-smoothstreaming-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/ced86e8f7c0cd24537ab9b470ab306c1/transformed/media3-exoplayer-smoothstreaming-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/e0b353b8a8ecb8c8f094a722df15c93c/transformed/media3-exoplayer-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/e0b353b8a8ecb8c8f094a722df15c93c/transformed/media3-exoplayer-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.webkit:webkit:1.12.1] /Users/<USER>/.gradle/caches/transforms-3/3dda310a8ed85dc3ca21fb8b0fffc6d4/transformed/webkit-1.12.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] /Users/<USER>/.gradle/caches/transforms-3/3dda310a8ed85dc3ca21fb8b0fffc6d4/transformed/webkit-1.12.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/e1432422334461c5abf599b8dbddd270/transformed/media-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/e1432422334461c5abf599b8dbddd270/transformed/media-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/04cabad0d9956a25f5c60d22b5bd2b74/transformed/core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/04cabad0d9956a25f5c60d22b5bd2b74/transformed/core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/ba1305210d7b1c0f3520def841e55ed5/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/ba1305210d7b1c0f3520def841e55ed5/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/7229cf67bf0c344836cb161dd3a335cd/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/7229cf67bf0c344836cb161dd3a335cd/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/573cde57e17d30d829a1457e03438b3b/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/573cde57e17d30d829a1457e03438b3b/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/7c7209f139f051b6f880ed68f5a57e51/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/7c7209f139f051b6f880ed68f5a57e51/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/7958b33ddf963dab1b5dcc9d2ead36f9/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/7958b33ddf963dab1b5dcc9d2ead36f9/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/f33cf5ea82bedd4794279fb2563bbcfd/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/f33cf5ea82bedd4794279fb2563bbcfd/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/7c6e544787d5ec564aa20635c64443cb/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/7c6e544787d5ec564aa20635c64443cb/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/48b2b5e224a2f110a57147aa68b2a2c6/transformed/room-runtime-2.6.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/48b2b5e224a2f110a57147aa68b2a2c6/transformed/room-runtime-2.6.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] /Users/<USER>/.gradle/caches/transforms-3/3baaf18cfc31dee895f958d100ebe0dc/transformed/sqlite-framework-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] /Users/<USER>/.gradle/caches/transforms-3/3baaf18cfc31dee895f958d100ebe0dc/transformed/sqlite-framework-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] /Users/<USER>/.gradle/caches/transforms-3/f3aee0677b7e05257474d20122f2bb0e/transformed/sqlite-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] /Users/<USER>/.gradle/caches/transforms-3/f3aee0677b7e05257474d20122f2bb0e/transformed/sqlite-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] /Users/<USER>/.gradle/caches/transforms-3/3f012daa273a3805a86fce503ea546a1/transformed/security-crypto-1.1.0-alpha06/AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] /Users/<USER>/.gradle/caches/transforms-3/3f012daa273a3805a86fce503ea546a1/transformed/security-crypto-1.1.0-alpha06/AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] /Users/<USER>/.gradle/caches/transforms-3/fdbc2740bba006d61e7aab42317d51d6/transformed/exifinterface-1.3.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] /Users/<USER>/.gradle/caches/transforms-3/fdbc2740bba006d61e7aab42317d51d6/transformed/exifinterface-1.3.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/dbb2724e4d71d30ed9011fddbb54a8a2/transformed/firebase-components-18.0.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/dbb2724e4d71d30ed9011fddbb54a8a2/transformed/firebase-components-18.0.0/AndroidManifest.xml:18:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/a7a8e61d0ac5ab0e055aa6cb087c14e4/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/a7a8e61d0ac5ab0e055aa6cb087c14e4/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/a513521685cc88bfc2f102ef1bd90193/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/a513521685cc88bfc2f102ef1bd90193/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/95b907667c7473273b1264d0b8c0fdee/transformed/localbroadcastmanager-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/95b907667c7473273b1264d0b8c0fdee/transformed/localbroadcastmanager-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/35d396fc589e5d0c8f26e60dad7a1ae6/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/35d396fc589e5d0c8f26e60dad7a1ae6/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/d2a074df3f50ee54bbde0b09e0de1067/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/d2a074df3f50ee54bbde0b09e0de1067/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/d8217dc7863bca0dfd259441afb86ea5/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/d8217dc7863bca0dfd259441afb86ea5/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/709a26a8a6ca23234da41827a47e3a52/transformed/firebase-encoders-json-18.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/709a26a8a6ca23234da41827a47e3a52/transformed/firebase-encoders-json-18.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/transforms-3/2c94fb9cbc8b6ff89f502efe195a5918/transformed/transport-api-3.1.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/transforms-3/2c94fb9cbc8b6ff89f502efe195a5918/transformed/transport-api-3.1.0/AndroidManifest.xml:18:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/5626ace690c7419151e0238d162f02e3/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/5626ace690c7419151e0238d162f02e3/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a52f06f50a7cb96f36baa4ddcc0a8983/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a52f06f50a7cb96f36baa4ddcc0a8983/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/04f10413b4ea7b9417c6def6ec841244/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/04f10413b4ea7b9417c6def6ec841244/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f33e7f7b42507cae78e6d9a0a6a403c0/transformed/core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f33e7f7b42507cae78e6d9a0a6a403c0/transformed/core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f41057d02a4a56c6252621dbb0e21c14/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/f41057d02a4a56c6252621dbb0e21c14/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a209b2181c5c40a4c035c4b3d9726a22/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a209b2181c5c40a4c035c4b3d9726a22/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/209c62e829ddce36887d6d5e265a2954/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/209c62e829ddce36887d6d5e265a2954/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.multidex:multidex:2.0.1] /Users/<USER>/.gradle/caches/transforms-3/e476e707926cd306439bf40c0718da39/transformed/multidex-2.0.1/AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] /Users/<USER>/.gradle/caches/transforms-3/e476e707926cd306439bf40c0718da39/transformed/multidex-2.0.1/AndroidManifest.xml:20:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/transforms-3/8d14721d707f116d0540e055bec12a96/transformed/relinker-1.4.5/AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/transforms-3/8d14721d707f116d0540e055bec12a96/transformed/relinker-1.4.5/AndroidManifest.xml:5:5-43
MERGED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:7:5-9:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [:flutter_secure_storage] /Users/<USER>/Desktop/59/build/flutter_secure_storage/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-50
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Desktop/59/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Desktop/59/android/app/src/debug/AndroidManifest.xml
provider#dev.fluttercommunity.plus.share.ShareFileProvider
ADDED from [:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:13:9-21:20
	android:grantUriPermissions
		ADDED from [:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:17:13-47
	android:authorities
		ADDED from [:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-74
	android:exported
		ADDED from [:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:16:13-37
	android:name
		ADDED from [:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-77
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:18:13-20:68
	android:resource
		ADDED from [:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:20:17-65
	android:name
		ADDED from [:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:19:17-67
receiver#dev.fluttercommunity.plus.share.SharePlusPendingIntent
ADDED from [:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:26:9-32:20
	android:exported
		ADDED from [:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:28:13-37
	android:name
		ADDED from [:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:27:13-82
intent-filter#action:name:EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:29:13-31:29
action#EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:30:17-65
	android:name
		ADDED from [:share_plus] /Users/<USER>/Desktop/59/build/share_plus/intermediates/merged_manifest/debug/AndroidManifest.xml:30:25-62
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_analytics] /Users/<USER>/Desktop/59/build/firebase_analytics/intermediates/merged_manifest/debug/AndroidManifest.xml:12:9-16:19
MERGED from [:firebase_core] /Users/<USER>/Desktop/59/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] /Users/<USER>/Desktop/59/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-analytics-ktx:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/c8b8cf0be5b39564d368c9c946066111/transformed/firebase-analytics-ktx-22.4.0/AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-analytics-ktx:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/c8b8cf0be5b39564d368c9c946066111/transformed/firebase-analytics-ktx-22.4.0/AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-messaging-ktx:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/72e57fc24a13f1cb41009954b9a12311/transformed/firebase-messaging-ktx-24.1.1/AndroidManifest.xml:23:9-29:19
MERGED from [com.google.firebase:firebase-messaging-ktx:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/72e57fc24a13f1cb41009954b9a12311/transformed/firebase-messaging-ktx-24.1.1/AndroidManifest.xml:23:9-29:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:54:9-63:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:30:9-36:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:30:9-36:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/e9fa5516e1bccc9a746fa45b1bb2aaf9/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/e9fa5516e1bccc9a746fa45b1bb2aaf9/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/d8217dc7863bca0dfd259441afb86ea5/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/d8217dc7863bca0dfd259441afb86ea5/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-analytics-ktx:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/c8b8cf0be5b39564d368c9c946066111/transformed/firebase-analytics-ktx-22.4.0/AndroidManifest.xml:10:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_analytics] /Users/<USER>/Desktop/59/build/firebase_analytics/intermediates/merged_manifest/debug/AndroidManifest.xml:12:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar
ADDED from [:firebase_analytics] /Users/<USER>/Desktop/59/build/firebase_analytics/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-15:85
	android:value
		ADDED from [:firebase_analytics] /Users/<USER>/Desktop/59/build/firebase_analytics/intermediates/merged_manifest/debug/AndroidManifest.xml:15:17-82
	android:name
		ADDED from [:firebase_analytics] /Users/<USER>/Desktop/59/build/firebase_analytics/intermediates/merged_manifest/debug/AndroidManifest.xml:14:17-128
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] /Users/<USER>/Desktop/59/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] /Users/<USER>/Desktop/59/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] /Users/<USER>/Desktop/59/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:10:17-124
meta-data#com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar
ADDED from [com.google.firebase:firebase-analytics-ktx:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/c8b8cf0be5b39564d368c9c946066111/transformed/firebase-analytics-ktx-22.4.0/AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-analytics-ktx:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/c8b8cf0be5b39564d368c9c946066111/transformed/firebase-analytics-ktx-22.4.0/AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-analytics-ktx:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/c8b8cf0be5b39564d368c9c946066111/transformed/firebase-analytics-ktx-22.4.0/AndroidManifest.xml:12:17-129
meta-data#com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar
ADDED from [com.google.firebase:firebase-messaging-ktx:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/72e57fc24a13f1cb41009954b9a12311/transformed/firebase-messaging-ktx-24.1.1/AndroidManifest.xml:26:13-28:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging-ktx:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/72e57fc24a13f1cb41009954b9a12311/transformed/firebase-messaging-ktx-24.1.1/AndroidManifest.xml:28:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging-ktx:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/72e57fc24a13f1cb41009954b9a12311/transformed/firebase-messaging-ktx-24.1.1/AndroidManifest.xml:27:17-129
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/f493bdc918884138078b7373670d0c67/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/f493bdc918884138078b7373670d0c67/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:47:13-82
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:50:13-52:29
	android:priority
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:50:28-51
action#com.google.firebase.MESSAGING_EVENT
ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:51:17-78
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:51:25-75
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] /Users/<USER>/.gradle/caches/transforms-3/ca89ffd761fcd8a186188c7f6d23e3fd/transformed/firebase-messaging-24.1.1/AndroidManifest.xml:61:17-119
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/b572e6391ee6f610a1ac5fb044e145f0/transformed/play-services-measurement-impl-22.4.0/AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/b572e6391ee6f610a1ac5fb044e145f0/transformed/play-services-measurement-impl-22.4.0/AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/3903fc12867b744337a3d399dce716f0/transformed/play-services-ads-identifier-18.0.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/3903fc12867b744337a3d399dce716f0/transformed/play-services-ads-identifier-18.0.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/a4790809080a1312f4efb82681ff771f/transformed/play-services-measurement-sdk-api-22.4.0/AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/a4790809080a1312f4efb82681ff771f/transformed/play-services-measurement-sdk-api-22.4.0/AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/a4790809080a1312f4efb82681ff771f/transformed/play-services-measurement-sdk-api-22.4.0/AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/a4790809080a1312f4efb82681ff771f/transformed/play-services-measurement-sdk-api-22.4.0/AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/a4790809080a1312f4efb82681ff771f/transformed/play-services-measurement-sdk-api-22.4.0/AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/a4790809080a1312f4efb82681ff771f/transformed/play-services-measurement-sdk-api-22.4.0/AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:27:22-79
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/AndroidManifest.xml:34:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/cc0751cbfa3fb04b72d3b4ce3d4fac45/transformed/firebase-installations-18.0.0/AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/e9fa5516e1bccc9a746fa45b1bb2aaf9/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/e9fa5516e1bccc9a746fa45b1bb2aaf9/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/e9fa5516e1bccc9a746fa45b1bb2aaf9/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/AndroidManifest.xml:36:17-109
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:10:13-82
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/Desktop/59/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:29:17-63
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] /Users/<USER>/Desktop/59/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] /Users/<USER>/Desktop/59/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] /Users/<USER>/Desktop/59/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] /Users/<USER>/Desktop/59/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:9:13-74
uses-permission#android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS
ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-89
	android:name
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:7:22-86
receiver#me.carda.awesome_notifications.DartNotificationActionReceiver
ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:10:9-12:39
	android:exported
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-36
	android:name
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-89
receiver#me.carda.awesome_notifications.DartDismissedNotificationReceiver
ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:13:9-15:39
	android:exported
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-36
	android:name
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-92
receiver#me.carda.awesome_notifications.DartScheduledNotificationReceiver
ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:16:9-18:39
	android:exported
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:18:13-36
	android:name
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:17:13-92
receiver#me.carda.awesome_notifications.DartRefreshSchedulesReceiver
ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:19:9-33:20
	android:enabled
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:21:13-35
	android:exported
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:22:13-36
	android:name
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-87
intent-filter#action:name:android.app.action.SCHEDULE_EXACT_ALARM_PERMISSION_STATE_CHANGED+action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.LOCKED_BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.QUICKBOOT_POWERON+action:name:com.htc.intent.action.QUICKBOOT_POWERON+category:name:android.intent.category.DEFAULT
ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:23:13-32:29
category#android.intent.category.DEFAULT
ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:24:17-76
	android:name
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:24:27-73
action#android.intent.action.BOOT_COMPLETED
ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:26:17-79
	android:name
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:26:25-76
action#android.intent.action.LOCKED_BOOT_COMPLETED
ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:27:17-86
	android:name
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:27:25-83
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:28:17-84
	android:name
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:28:25-81
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:29:17-82
	android:name
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:29:25-79
action#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:30:17-82
	android:name
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:30:25-79
action#android.app.action.SCHEDULE_EXACT_ALARM_PERMISSION_STATE_CHANGED
ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:31:17-107
	android:name
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:31:25-104
service#me.carda.awesome_notifications.DartBackgroundService
ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:35:9-38:72
	android:exported
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:37:13-37
	android:permission
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:38:13-69
	android:name
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:36:13-80
service#me.carda.awesome_notifications.core.services.ForegroundService
ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:39:9-44:43
MERGED from [me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:15:9-20:43
MERGED from [me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:15:9-20:43
	android:enabled
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:42:13-37
	android:stopWithTask
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:44:13-40
	android:foregroundServiceType
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:43:13-54
	android:name
		ADDED from [:awesome_notifications] /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/merged_manifest/debug/AndroidManifest.xml:40:13-90
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-10:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:9:13-90
	android:name
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:9:21-87
activity#com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity
ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:14:9-18:47
	android:exported
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:17:13-37
	android:configChanges
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:16:13-137
	android:theme
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:18:13-44
	android:name
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-112
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity
ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:19:9-22:55
	android:exported
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:21:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:22:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-120
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity
ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:23:9-26:55
	android:exported
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:25:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:26:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:24:13-114
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance
ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:27:9-31:55
	android:launchMode
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:30:13-48
	android:exported
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:29:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:31:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:28:13-134
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance
ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:32:9-36:55
	android:launchMode
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:35:13-48
	android:exported
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:34:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:36:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:33:13-128
receiver#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver
ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:38:9-41:40
	android:enabled
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:40:13-35
	android:exported
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:41:13-37
	android:name
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:39:13-119
meta-data#io.flutter.embedded_views_preview
ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:43:9-45:36
	android:value
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:45:13-33
	android:name
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/merged_manifest/debug/AndroidManifest.xml:44:13-61
uses-permission#android.permission.BIND_NOTIFICATION_LISTENER_SERVICE
ADDED from [me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:9:5-12:47
	android:maxSdkVersion
		ADDED from [me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:11:9-35
	tools:ignore
		ADDED from [me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:12:9-44
	android:name
		ADDED from [me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:10:9-77
service#me.carda.awesome_notifications.core.managers.StatusBarManager
ADDED from [me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:21:9-28:19
	android:label
		ADDED from [me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:24:13-61
	android:exported
		ADDED from [me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:23:13-36
	android:name
		ADDED from [me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:22:13-89
intent-filter#action:name:android.service.notification.NotificationListenerService
ADDED from [me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:25:13-27:29
action#android.service.notification.NotificationListenerService
ADDED from [me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:26:17-99
	android:name
		ADDED from [me.carda:AndroidAwnCore:0.10.0] /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/AndroidManifest.xml:26:25-96
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/b572e6391ee6f610a1ac5fb044e145f0/transformed/play-services-measurement-impl-22.4.0/AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/b572e6391ee6f610a1ac5fb044e145f0/transformed/play-services-measurement-impl-22.4.0/AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/AndroidManifest.xml:40:13-87
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/ce98a87c43f97d057c48b3433fc10c79/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/ce98a87c43f97d057c48b3433fc10c79/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/ce98a87c43f97d057c48b3433fc10c79/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/ce98a87c43f97d057c48b3433fc10c79/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:19-85
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/16693b5e1a09ace5ff4ca3456cfe7953/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/066ad0703a4a4a7e35fddd8e645492a3/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/066ad0703a4a4a7e35fddd8e645492a3/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/a7a8e61d0ac5ab0e055aa6cb087c14e4/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/a7a8e61d0ac5ab0e055aa6cb087c14e4/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/16693b5e1a09ace5ff4ca3456cfe7953/transformed/emoji2-1.3.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/16693b5e1a09ace5ff4ca3456cfe7953/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/16693b5e1a09ace5ff4ca3456cfe7953/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/16693b5e1a09ace5ff4ca3456cfe7953/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/16693b5e1a09ace5ff4ca3456cfe7953/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/16693b5e1a09ace5ff4ca3456cfe7953/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/16693b5e1a09ace5ff4ca3456cfe7953/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/066ad0703a4a4a7e35fddd8e645492a3/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/066ad0703a4a4a7e35fddd8e645492a3/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/066ad0703a4a4a7e35fddd8e645492a3/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b21b8ff5629daa6ae82a2981003c3e1/transformed/window-1.2.0/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b21b8ff5629daa6ae82a2981003c3e1/transformed/window-1.2.0/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b21b8ff5629daa6ae82a2981003c3e1/transformed/window-1.2.0/AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b21b8ff5629daa6ae82a2981003c3e1/transformed/window-1.2.0/AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b21b8ff5629daa6ae82a2981003c3e1/transformed/window-1.2.0/AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1b21b8ff5629daa6ae82a2981003c3e1/transformed/window-1.2.0/AndroidManifest.xml:27:13-51
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/transforms-3/1f7636bc79bcca6cfc240d5569ab97dd/transformed/ads-adservices-1.1.0-beta11/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/transforms-3/1f7636bc79bcca6cfc240d5569ab97dd/transformed/ads-adservices-1.1.0-beta11/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/transforms-3/1f7636bc79bcca6cfc240d5569ab97dd/transformed/ads-adservices-1.1.0-beta11/AndroidManifest.xml:24:13-50
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/7178fedea55c950c8017bbfb028a9590/transformed/play-services-basement-18.5.0/AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/7178fedea55c950c8017bbfb028a9590/transformed/play-services-basement-18.5.0/AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/transforms-3/7178fedea55c950c8017bbfb028a9590/transformed/play-services-basement-18.5.0/AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
permission#com.example.kft.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
uses-permission#com.example.kft.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/48b2b5e224a2f110a57147aa68b2a2c6/transformed/room-runtime-2.6.1/AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/48b2b5e224a2f110a57147aa68b2a2c6/transformed/room-runtime-2.6.1/AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/48b2b5e224a2f110a57147aa68b2a2c6/transformed/room-runtime-2.6.1/AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/48b2b5e224a2f110a57147aa68b2a2c6/transformed/room-runtime-2.6.1/AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/48b2b5e224a2f110a57147aa68b2a2c6/transformed/room-runtime-2.6.1/AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/6c25ddbe3fe7bdcf40fbbcead815e22b/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/d8217dc7863bca0dfd259441afb86ea5/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/d8217dc7863bca0dfd259441afb86ea5/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/d8217dc7863bca0dfd259441afb86ea5/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/4667b4f7d2b2e5ab3802f3beefa05edf/transformed/transport-runtime-3.1.9/AndroidManifest.xml:33:13-132
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:19:5-86
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:19:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:20:5-87
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:20:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:23:5-81
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:23:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:24:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:24:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:27:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:27:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:28:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:28:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:31:5-84
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:31:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:34:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:34:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:37:5-91
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:37:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:38:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:38:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:39:5-93
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:39:22-90
uses-permission#android.permission.READ_APP_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:42:5-73
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:42:22-70
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:45:5-82
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:45:22-79
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:46:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:46:22-80
uses-permission#me.everything.badger.permission.BADGE_COUNT_READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:49:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:49:22-85
uses-permission#me.everything.badger.permission.BADGE_COUNT_WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:50:5-89
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:50:22-86
