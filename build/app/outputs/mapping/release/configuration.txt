# The proguard configuration file for the following section is /Users/<USER>/Desktop/59/build/app/intermediates/default_proguard_files/global/proguard-android-optimize.txt-8.2.1
# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html
#
# Starting with version 2.2 of the Android plugin for Gradle, this file is distributed together with
# the plugin and unpacked at build-time. The files in $ANDROID_HOME are no longer maintained and
# will be ignored by new version of the Android plugin for Gradle.

# Optimizations: If you don't want to optimize, use the proguard-android.txt configuration file
# instead of this one, which turns off the optimization flags.
# Adding optimization introduces certain risks, since for example not all optimizations performed by
# ProGuard works on all versions of Dalvik.  The following flags turn off various optimizations
# known to have issues, but the list may not be complete or up to date. (The "arithmetic"
# optimization can be used if you are only targeting Android 2.0 or later.)  Make sure you test
# thoroughly if you go this route.
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification

-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose

# Preserve some attributes that may be required for reflection.
-keepattributes AnnotationDefault,
                EnclosingMethod,
                InnerClasses,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations,
                Signature

-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService
-keep public class com.google.android.vending.licensing.ILicensingService
-dontnote com.android.vending.licensing.ILicensingService
-dontnote com.google.vending.licensing.ILicensingService
-dontnote com.google.android.vending.licensing.ILicensingService

# For native methods, see http://proguard.sourceforge.net/manual/examples.html#native
-keepclasseswithmembernames,includedescriptorclasses class * {
    native <methods>;
}

# Keep setters in Views so that animations can still work.
-keepclassmembers public class * extends android.view.View {
    void set*(***);
    *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick.
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}

# For enumeration classes, see http://proguard.sourceforge.net/manual/examples.html#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

# Preserve annotated Javascript interface methods.
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# The support libraries contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version. We know about them, and they are safe.
-dontnote android.support.**
-dontnote androidx.**
-dontwarn android.support.**
-dontwarn androidx.**

# This class is deprecated, but remains for backward compatibility.
-dontwarn android.util.FloatMath

# Understand the @Keep support annotation.
-keep class android.support.annotation.Keep
-keep class androidx.annotation.Keep

-keep @android.support.annotation.Keep class * {*;}
-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <init>(...);
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

# These classes are duplicated between android.jar and org.apache.http.legacy.jar.
-dontnote org.apache.http.**
-dontnote android.net.http.**

# These classes are duplicated between android.jar and core-lambda-stubs.jar.
-dontnote java.lang.invoke.**

# End of content from /Users/<USER>/Desktop/59/build/app/intermediates/default_proguard_files/global/proguard-android-optimize.txt-8.2.1
# The proguard configuration file for the following section is /Users/<USER>/flutter/packages/flutter_tools/gradle/flutter_proguard_rules.pro
# Build the ephemeral app in a module project.
# Prevents: Warning: library class <plugin-package> depends on program class io.flutter.plugin.**
# This is due to plugins (libraries) depending on the embedding (the program jar)
-dontwarn io.flutter.plugin.**

# The android.** package is provided by the OS at runtime.
-dontwarn android.**

# In some cases, R8 is incorrectly stripping plugin classes. Keep
# all implementations of FlutterPlugin until we can determine
# why this is the case.
# See https://github.com/flutter/flutter/issues/154580.
-if class * implements io.flutter.embedding.engine.plugins.FlutterPlugin
-keep,allowshrinking,allowobfuscation class <1>

# End of content from /Users/<USER>/flutter/packages/flutter_tools/gradle/flutter_proguard_rules.pro
# The proguard configuration file for the following section is /Users/<USER>/Desktop/59/android/app/proguard-rules.pro
# Flutter Local Notifications - keep generic type signatures
-keepattributes Signature
-keep class com.dexterous.** { *; }
-keep class com.dexterous.flutterlocalnotifications.** { *; }
-keep class com.google.gson.reflect.TypeToken { *; } 
# End of content from /Users/<USER>/Desktop/59/android/app/proguard-rules.pro
# The proguard configuration file for the following section is /Users/<USER>/Desktop/59/build/app/intermediates/aapt_proguard_file/release/aapt_rules.txt
-keep class android.app.Application { <init>(); }
-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.profileinstaller.ProfileInstallReceiver { <init>(); }
-keep class androidx.room.MultiInstanceInvalidationService { <init>(); }
-keep class androidx.startup.InitializationProvider { <init>(); }
-keep class com.example.kft.MainActivity { <init>(); }
-keep class com.google.android.datatransport.runtime.backends.TransportBackendDiscovery { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService { <init>(); }
-keep class com.google.android.gms.common.api.GoogleApiActivity { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementJobService { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementReceiver { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementService { <init>(); }
-keep class com.google.android.gms.metadata.ModuleDependencies { <init>(); }
-keep class com.google.firebase.components.ComponentDiscoveryService { <init>(); }
-keep class com.google.firebase.iid.FirebaseInstanceIdReceiver { <init>(); }
-keep class com.google.firebase.messaging.FirebaseMessagingService { <init>(); }
-keep class com.google.firebase.provider.FirebaseInitProvider { <init>(); }
-keep class com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver { <init>(); }
-keep class com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity { <init>(); }
-keep class com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance { <init>(); }
-keep class com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity { <init>(); }
-keep class com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance { <init>(); }
-keep class com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity { <init>(); }
-keep class dev.fluttercommunity.plus.share.ShareFileProvider { <init>(); }
-keep class dev.fluttercommunity.plus.share.SharePlusPendingIntent { <init>(); }
-keep class io.flutter.plugins.imagepicker.ImagePickerFileProvider { <init>(); }
-keep class io.flutter.plugins.urllauncher.WebViewActivity { <init>(); }
-keep class me.carda.awesome_notifications.DartBackgroundService { <init>(); }
-keep class me.carda.awesome_notifications.DartDismissedNotificationReceiver { <init>(); }
-keep class me.carda.awesome_notifications.DartNotificationActionReceiver { <init>(); }
-keep class me.carda.awesome_notifications.DartRefreshSchedulesReceiver { <init>(); }
-keep class me.carda.awesome_notifications.DartScheduledNotificationReceiver { <init>(); }
-keep class me.carda.awesome_notifications.core.managers.StatusBarManager { <init>(); }
-keep class me.carda.awesome_notifications.core.services.ForegroundService { <init>(); }
-keep class android.widget.SearchView { <init>(android.content.Context); }

-keep class android.widget.Space { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.app.AlertController$RecycleListView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ActionMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ExpandedMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ListMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContainer { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContextView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarOverlayLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActivityChooserView$InnerLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.AlertDialogLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ButtonBarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ContentFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.DialogTitle { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsLinearLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.SearchView$SearchAutoComplete { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.SwitchCompat { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.Toolbar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ViewStubCompat { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.browser.browseractions.BrowserActionsFallbackMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.constraintlayout.helper.widget.Flow { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.constraintlayout.widget.ConstraintLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.coordinatorlayout.widget.CoordinatorLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.core.widget.NestedScrollView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.fragment.app.FragmentContainerView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.preference.UnPressableLinearLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.preference.internal.PreferenceImageView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.recyclerview.widget.RecyclerView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.appbar.MaterialToolbar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.button.MaterialButton { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.button.MaterialButtonToggleGroup { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.chip.Chip { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.datepicker.MaterialCalendarGridView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.BaselineLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.CheckableImageButton { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.ClippableRoundedCornerLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.NavigationMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.NavigationMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.TouchObserverFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.snackbar.Snackbar$SnackbarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.snackbar.SnackbarContentLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.textfield.TextInputEditText { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.textfield.TextInputLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.ChipTextInputComboView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.ClockFaceView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.ClockHandView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.TimePickerView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.pichillilorenzo.flutter_inappwebview_android.pull_to_refresh.PullToRefreshLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.pichillilorenzo.flutter_inappwebview_android.webview.in_app_webview.InAppWebView { <init>(android.content.Context, android.util.AttributeSet); }

-keepclassmembers class * { *** closeButtonClicked(android.view.MenuItem); }

-keepclassmembers class * { *** goBackButtonClicked(android.view.MenuItem); }

-keepclassmembers class * { *** goForwardButtonClicked(android.view.MenuItem); }

-keepclassmembers class * { *** reloadButtonClicked(android.view.MenuItem); }

-keepclassmembers class * { *** shareButtonClicked(android.view.MenuItem); }


# End of content from /Users/<USER>/Desktop/59/build/app/intermediates/aapt_proguard_file/release/aapt_rules.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/proguard.txt
# Can be removed once we pull in a dependency on firebase-common that includes
# https://github.com/firebase/firebase-android-sdk/pull/1472/commits/856f1ca1151cdd88679bbc778892f23dfa34fc06#diff-a2ed34b5a38b4c6c686b09e54865eb48
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzmd {
  <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/971c254c3ac4fc5c5f58473fe0f72958/transformed/play-services-measurement-api-22.4.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/proguard.txt
-dontwarn com.google.firebase.platforminfo.KotlinDetector
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from /Users/<USER>/.gradle/caches/transforms-3/912f9f75c0fa440b04572ef9ad92fd2e/transformed/firebase-common-21.0.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/consumer_proguard_dir/release/lib0/proguard.txt
# Keep necessary SQLite and SQLCipher classes
-keep class org.sqlite.** { *; }
-keep class org.sqlite.database.** { *; }
-keep,includedescriptorclasses class net.sqlcipher.** { *; }
-keep,includedescriptorclasses interface net.sqlcipher.** { *; }

# Keep all public (models) classes in awesome packages
-keep public class me.carda.** { *; }

# Gson uses generic type information stored in a class file when working with fields. Proguard
# removes such information by default, so configure it to keep all of it.
-keepattributes Signature

# Gson specific classes
-dontwarn sun.misc.**
-keep class com.google.gson.stream.** { *; }

# Application classes that will be serialized/deserialized over Gson
-keep class com.google.gson.examples.android.model.** { <fields>; }

# Retain generic signatures of TypeToken and its subclasses with R8 version 3.0 and higher.
-keep,allowobfuscation,allowshrinking class com.google.gson.reflect.TypeToken
-keep,allowobfuscation,allowshrinking class * extends com.google.gson.reflect.TypeToken

# Prevent R8 from nullifying Data object members annotated with SerializedName
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.SerializedName <fields>;
}
# End of content from /Users/<USER>/Desktop/59/build/awesome_notifications/intermediates/consumer_proguard_dir/release/lib0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/consumer_proguard_dir/release/lib0/proguard.txt
# WebView
-keepattributes *JavascriptInterface*
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}
-keepclassmembers class * extends android.webkit.WebViewClient {
    public void *(android.webkit.WebView, java.lang.String, android.graphics.Bitmap);
    public boolean *(android.webkit.WebView, java.lang.String);
    public void *(android.webkit.webView, jav.lang.String);
}
-keepclassmembers class com.pichillilorenzo.flutter_inappwebview_android$JavaScriptBridgeInterface {
     <fields>;
     <methods>;
     public *;
     private *;
}
-keep class com.pichillilorenzo.flutter_inappwebview_android.** { *; }

-dontwarn android.window.BackEvent
# End of content from /Users/<USER>/Desktop/59/build/flutter_inappwebview_android/intermediates/consumer_proguard_dir/release/lib0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/Desktop/59/build/flutter_plugin_android_lifecycle/intermediates/consumer_proguard_dir/release/lib0/proguard.txt
# The point of this package is to specify that a dependent plugin intends to
# use the AndroidX lifecycle classes. Make sure no R8 heuristics shrink classes
# brought in by the embedding's pom.
#
# This isn't strictly needed since by definition, plugins using Android
# lifecycles should implement DefaultLifecycleObserver and therefore keep it
# from being shrunk. But there seems to be an R8 bug so this needs to stay
# https://issuetracker.google.com/issues/142778206.
-keep class androidx.lifecycle.DefaultLifecycleObserver

# End of content from /Users/<USER>/Desktop/59/build/flutter_plugin_android_lifecycle/intermediates/consumer_proguard_dir/release/lib0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/proguard.txt
# Keep necessary SQLite and SQLCipher classes
-keep class org.sqlite.** { *; }
-keep class org.sqlite.database.** { *; }
-keep,includedescriptorclasses class net.sqlcipher.** { *; }
-keep,includedescriptorclasses interface net.sqlcipher.** { *; }

# Keep all public (models) classes in awesome packages
-keep public class me.carda.** { *; }


# Gson specific classes
-dontwarn sun.misc.**
-keep class com.google.gson.stream.** { *; }

# Application classes that will be serialized/deserialized over Gson
-keep class com.google.gson.examples.android.model.** { <fields>; }

# Prevent stripping TypeAdapter, TypeAdapterFactory, JsonSerializer, JsonDeserializer
-keep class * extends com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Prevent R8 from nullifying Data object members annotated with SerializedName
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.SerializedName <fields>;
}
# End of content from /Users/<USER>/.gradle/caches/transforms-3/b164e98e27be739dbbc3fd4adbe7361f/transformed/AndroidAwnCore-0.10.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/76d2e61486788019e765d06df0a9d1c7/transformed/preference-1.2.1/proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Preference objects are inflated via reflection
-keep public class androidx.preference.Preference {
    public <init>(android.content.Context, android.util.AttributeSet);
}
-keep public class * extends androidx.preference.Preference {
    public <init>(android.content.Context, android.util.AttributeSet);
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/76d2e61486788019e765d06df0a9d1c7/transformed/preference-1.2.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/467a3199382fb71113479a03289e69bb/transformed/material-1.12.0/proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# CoordinatorLayout resolves the behaviors of its child components with reflection.
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>();
}

# Make sure we keep annotations for CoordinatorLayout's DefaultBehavior
-keepattributes RuntimeVisible*Annotation*

# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# AppCompatViewInflater reads the viewInflaterClass theme attribute which then
# reflectively instantiates MaterialComponentsViewInflater using the no-argument
# constructor. We only need to keep this constructor and the class name if
# AppCompatViewInflater is also being kept.
-if class androidx.appcompat.app.AppCompatViewInflater
-keep class com.google.android.material.theme.MaterialComponentsViewInflater {
    <init>();
}


# End of content from /Users/<USER>/.gradle/caches/transforms-3/467a3199382fb71113479a03289e69bb/transformed/material-1.12.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/14eb9eff792770dbb80324f3dd5a4ec0/transformed/appcompat-1.7.0/proguard.txt
# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl* {
  <methods>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/14eb9eff792770dbb80324f3dd5a4ec0/transformed/appcompat-1.7.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzmd {
  <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/042c0a1d88a9468a21da4eb15a87c92d/transformed/play-services-measurement-22.4.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/0eb9e273a1b2a9288228e1c0f3a26ae5/transformed/play-services-measurement-sdk-22.4.0/proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzmd {
  <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/0eb9e273a1b2a9288228e1c0f3a26ae5/transformed/play-services-measurement-sdk-22.4.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/b572e6391ee6f610a1ac5fb044e145f0/transformed/play-services-measurement-impl-22.4.0/proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzmd {
  <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/b572e6391ee6f610a1ac5fb044e145f0/transformed/play-services-measurement-impl-22.4.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/a4790809080a1312f4efb82681ff771f/transformed/play-services-measurement-sdk-api-22.4.0/proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzmd {
  <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/a4790809080a1312f4efb82681ff771f/transformed/play-services-measurement-sdk-api-22.4.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/0663566f035547712eb9b8b2494af47f/transformed/play-services-measurement-base-22.4.0/proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzmd {
  <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/0663566f035547712eb9b8b2494af47f/transformed/play-services-measurement-base-22.4.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/ce98a87c43f97d057c48b3433fc10c79/transformed/play-services-base-18.5.0/proguard.txt
# b/35135904 Ensure that proguard will not strip the mResultGuardian.
-keepclassmembers class com.google.android.gms.common.api.internal.BasePendingResult {
  com.google.android.gms.common.api.internal.BasePendingResult$ReleasableResultGuardian mResultGuardian;
}



# End of content from /Users/<USER>/.gradle/caches/transforms-3/ce98a87c43f97d057c48b3433fc10c79/transformed/play-services-base-18.5.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/b330c0ccf3b73664bc475bc6a2ecad5a/transformed/transition-1.5.0/proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep a field in transition that is used to keep a reference to weakly-referenced object
-keepclassmembers class androidx.transition.ChangeBounds$* extends android.animation.AnimatorListenerAdapter {
  androidx.transition.ChangeBounds$ViewBounds mViewBounds;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/b330c0ccf3b73664bc475bc6a2ecad5a/transformed/transition-1.5.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/0d2d6b635765aae7af8bdd0b725fbf57/transformed/lifecycle-viewmodel-savedstate-2.7.0/proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>(androidx.lifecycle.SavedStateHandle);
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application,androidx.lifecycle.SavedStateHandle);
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/0d2d6b635765aae7af8bdd0b725fbf57/transformed/lifecycle-viewmodel-savedstate-2.7.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/3189a20ce2937e4ad2a2c0be7f3d4c6b/transformed/lifecycle-viewmodel-2.7.0/proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>();
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application);
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/3189a20ce2937e4ad2a2c0be7f3d4c6b/transformed/lifecycle-viewmodel-2.7.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/e1b997ea2da97acd8543f9995d2f4957/transformed/lifecycle-runtime-2.7.0/proguard.txt
-keepattributes AnnotationDefault,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations

-keepclassmembers enum androidx.lifecycle.Lifecycle$Event {
    <fields>;
}

-keep class * implements androidx.lifecycle.GeneratedAdapter {
    <init>(...);
}

-keepclassmembers class ** {
    @androidx.lifecycle.OnLifecycleEvent *;
}

# this rule is need to work properly when app is compiled with api 28, see b/142778206
# Also this rule prevents registerIn from being inlined.
-keepclassmembers class androidx.lifecycle.ReportFragment$LifecycleCallbacks { *; }
# End of content from /Users/<USER>/.gradle/caches/transforms-3/e1b997ea2da97acd8543f9995d2f4957/transformed/lifecycle-runtime-2.7.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/066ad0703a4a4a7e35fddd8e645492a3/transformed/lifecycle-process-2.7.0/proguard.txt
# this rule is need to work properly when app is compiled with api 28, see b/142778206
-keepclassmembers class * extends androidx.lifecycle.EmptyActivityLifecycleCallbacks { *; }
# End of content from /Users/<USER>/.gradle/caches/transforms-3/066ad0703a4a4a7e35fddd8e645492a3/transformed/lifecycle-process-2.7.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/0fa9bc69cc91a3fceb1f37075e9803f7/transformed/savedstate-1.2.1/proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

-keepclassmembers,allowobfuscation class * implements androidx.savedstate.SavedStateRegistry$AutoRecreated {
    <init>();
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/0fa9bc69cc91a3fceb1f37075e9803f7/transformed/savedstate-1.2.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/1b21b8ff5629daa6ae82a2981003c3e1/transformed/window-1.2.0/proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# A rule that will keep classes that implement SidecarInterface$SidecarCallback if Sidecar seems
# be used. See b/157286362 and b/165268619 for details.
# TODO(b/208543178) investigate how to pass header jar to R8 so we don't need this rule
-if class androidx.window.layout.adapter.sidecar.SidecarCompat {
  public *** setExtensionCallback(androidx.window.layout.adapter.sidecar.ExtensionInterfaceCompat$ExtensionCallbackInterface);
}
-keep class androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback,
 androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback {
  public *** onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState);
  public *** onWindowLayoutChanged(android.os.IBinder, androidx.window.sidecar.SidecarWindowLayoutInfo);
}
# Required for window area API reflection guard
-keep interface androidx.window.area.reflectionguard.* {*;}
# End of content from /Users/<USER>/.gradle/caches/transforms-3/1b21b8ff5629daa6ae82a2981003c3e1/transformed/window-1.2.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/5da07d3c3e3187b4c41a5ef4f813551c/transformed/rules/lib/META-INF/com.android.tools/r8-from-1.6.0/coroutines.pro
# Allow R8 to optimize away the FastServiceLoader.
# Together with ServiceLoader optimization in R8
# this results in direct instantiation when loading Dispatchers.Main
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatcherLoader {
    boolean FAST_SERVICE_LOADER_ENABLED return false;
}

-assumenosideeffects class kotlinx.coroutines.internal.FastServiceLoaderKt {
    boolean ANDROID_DETECTED return true;
}

# Disable support for "Missing Main Dispatcher", since we always have Android main dispatcher
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatchersKt {
    boolean SUPPORT_MISSING return false;
}

# Statically turn off all debugging facilities and assertions
-assumenosideeffects class kotlinx.coroutines.DebugKt {
    boolean getASSERTIONS_ENABLED() return false;
    boolean getDEBUG() return false;
    boolean getRECOVER_STACK_TRACES() return false;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/5da07d3c3e3187b4c41a5ef4f813551c/transformed/rules/lib/META-INF/com.android.tools/r8-from-1.6.0/coroutines.pro
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/1daaac5a186927a65383ae4a5b4d82f4/transformed/rules/lib/META-INF/proguard/androidx.datastore_datastore-preferences-core.pro
-keepclassmembers class * extends androidx.datastore.preferences.protobuf.GeneratedMessageLite {
    <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/1daaac5a186927a65383ae4a5b4d82f4/transformed/rules/lib/META-INF/proguard/androidx.datastore_datastore-preferences-core.pro
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/f0fe56acc288a4ec9d427b2bb031f99d/transformed/rules/lib/META-INF/com.android.tools/r8/coroutines.pro
# When editing this file, update the following files as well:
# - META-INF/proguard/coroutines.pro
# - META-INF/com.android.tools/proguard/coroutines.pro

# Most of volatile fields are updated with AFU and should not be mangled
-keepclassmembers class kotlinx.coroutines.** {
    volatile <fields>;
}

# Same story for the standard library's SafeContinuation that also uses AtomicReferenceFieldUpdater
-keepclassmembers class kotlin.coroutines.SafeContinuation {
    volatile <fields>;
}

# These classes are only required by kotlinx.coroutines.debug.AgentPremain, which is only loaded when
# kotlinx-coroutines-core is used as a Java agent, so these are not needed in contexts where ProGuard is used.
-dontwarn java.lang.instrument.ClassFileTransformer
-dontwarn sun.misc.SignalHandler
-dontwarn java.lang.instrument.Instrumentation
-dontwarn sun.misc.Signal

# Only used in `kotlinx.coroutines.internal.ExceptionsConstructor`.
# The case when it is not available is hidden in a `try`-`catch`, as well as a check for Android.
-dontwarn java.lang.ClassValue

# An annotation used for build tooling, won't be directly accessed.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
# End of content from /Users/<USER>/.gradle/caches/transforms-3/f0fe56acc288a4ec9d427b2bb031f99d/transformed/rules/lib/META-INF/com.android.tools/r8/coroutines.pro
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/b63904aecfed9d0a8fd4c495383605fd/transformed/play-services-tasks-18.2.0/proguard.txt


# End of content from /Users/<USER>/.gradle/caches/transforms-3/b63904aecfed9d0a8fd4c495383605fd/transformed/play-services-tasks-18.2.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/7178fedea55c950c8017bbfb028a9590/transformed/play-services-basement-18.5.0/proguard.txt
# Needed when building against pre-Marshmallow SDK.
-dontwarn android.security.NetworkSecurityPolicy

# Needed when building against Marshmallow SDK.
-dontwarn android.app.Notification

# Protobuf has references not on the Android boot classpath
-dontwarn sun.misc.Unsafe
-dontwarn libcore.io.Memory

# Annotations used during internal SDK shrinking.
-dontwarn com.google.android.apps.common.proguard.UsedBy*
-dontwarn com.google.android.apps.common.proguard.SideEffectFree

# Annotations referenced by the SDK but whose definitions are contained in
# non-required dependencies.
-dontwarn javax.annotation.**
-dontwarn org.checkerframework.**
-dontwarn com.google.errorprone.annotations.**
-dontwarn org.jspecify.annotations.NullMarked

# Annotations no longer exist. Suppression prevents ProGuard failures in
# SDKs which depend on earlier versions of play-services-basement.
-dontwarn com.google.android.gms.common.util.VisibleForTesting

# Proguard flags for consumers of the Google Play services SDK
# https://developers.google.com/android/guides/setup#add_google_play_services_to_your_project

# Keep SafeParcelable NULL value, needed for reflection by DowngradeableSafeParcel
-keepclassmembers public class com.google.android.gms.common.internal.safeparcel.SafeParcelable {
    public static final *** NULL;
}

# Needed for Parcelable/SafeParcelable classes & their creators to not get renamed, as they are
# found via reflection.
-keep class com.google.android.gms.common.internal.ReflectedParcelable
-keepnames class * implements com.google.android.gms.common.internal.ReflectedParcelable
-keepclassmembers class * implements android.os.Parcelable {
  public static final *** CREATOR;
}

# Keep the classes/members we need for client functionality.
-keep @interface android.support.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep androidX equivalent of above android.support to allow Jetification.
-keep @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep the names of classes/members we need for client functionality.
-keep @interface com.google.android.gms.common.annotation.KeepName
-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
  @com.google.android.gms.common.annotation.KeepName *;
}

# Keep Dynamite API entry points
-keep @interface com.google.android.gms.common.util.DynamiteApi
-keep @com.google.android.gms.common.util.DynamiteApi public class * {
  public <fields>;
  public <methods>;
}



# End of content from /Users/<USER>/.gradle/caches/transforms-3/7178fedea55c950c8017bbfb028a9590/transformed/play-services-basement-18.5.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/36494e92349f734744ccde87c40d8ff0/transformed/fragment-1.7.1/proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The default FragmentFactory creates Fragment instances using reflection
-if public class ** extends androidx.fragment.app.Fragment
-keepclasseswithmembers,allowobfuscation public class <1> {
    public <init>();
}

# FragmentTransition will reflectively lookup:
# androidx.transition.FragmentTransitionSupport
# We should ensure that we keep the constructor if the code using this is alive
-if class androidx.fragment.app.FragmentTransition {
   private static androidx.fragment.app.FragmentTransitionImpl resolveSupportImpl();
}
-keep class androidx.transition.FragmentTransitionSupport {
    public <init>();
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/36494e92349f734744ccde87c40d8ff0/transformed/fragment-1.7.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/842d29af1d335def8f2c52a86b671b27/transformed/media3-extractor-1.4.1/proguard.txt
# Proguard rules specific to the extractor module.

# Methods accessed via reflection in DefaultExtractorsFactory
-dontnote androidx.media3.decoder.flac.FlacExtractor
-keepclassmembers class androidx.media3.decoder.flac.FlacExtractor {
  <init>(int);
}
-dontnote androidx.media3.decoder.flac.FlacLibrary
-keepclassmembers class androidx.media3.decoder.flac.FlacLibrary {
  public static boolean isAvailable();
}
-dontnote androidx.media3.decoder.midi.MidiExtractor
-keepclassmembers class androidx.media3.decoder.midi.MidiExtractor {
  <init>();
}

# Don't warn about checkerframework and Kotlin annotations
-dontwarn org.checkerframework.**
-dontwarn kotlin.annotations.jvm.**
-dontwarn javax.annotation.**

# End of content from /Users/<USER>/.gradle/caches/transforms-3/842d29af1d335def8f2c52a86b671b27/transformed/media3-extractor-1.4.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/70b46f9c0b45310ed4d8abab7fb56baa/transformed/media3-datasource-1.4.1/proguard.txt
# Proguard rules specific to the DataSource module.

# Constant folding for resource integers may mean that a resource passed to this method appears to be unused. Keep the method to prevent this from happening.
-keepclassmembers class androidx.media3.datasource.RawResourceDataSource {
  public static android.net.Uri buildRawResourceUri(int);
}

# Constructors accessed via reflection in DefaultDataSource
-dontnote androidx.media3.datasource.rtmp.RtmpDataSource
-keepclassmembers class androidx.media3.datasource.rtmp.RtmpDataSource {
  <init>();
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/70b46f9c0b45310ed4d8abab7fb56baa/transformed/media3-datasource-1.4.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/51c855c05cda79b2e3daea3b6f815359/transformed/media3-common-1.4.1/proguard.txt
# Proguard rules specific to the common module.

# Don't warn about checkerframework and Kotlin annotations
-dontwarn org.checkerframework.**
-dontwarn kotlin.annotations.jvm.**
-dontwarn javax.annotation.**

# From https://github.com/google/guava/wiki/UsingProGuardWithGuava
-dontwarn java.lang.ClassValue
-dontwarn java.lang.SafeVarargs
-dontwarn javax.lang.model.element.Modifier
-dontwarn sun.misc.Unsafe

# Don't warn about Guava's compile-only dependencies.
# These lines are needed for ProGuard but not R8.
-dontwarn com.google.errorprone.annotations.**
-dontwarn com.google.j2objc.annotations.**
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement

# Workaround for https://issuetracker.google.com/issues/112297269
# This is needed for ProGuard but not R8.
-keepclassmembernames class com.google.common.base.Function { *; }

# End of content from /Users/<USER>/.gradle/caches/transforms-3/51c855c05cda79b2e3daea3b6f815359/transformed/media3-common-1.4.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/e0b353b8a8ecb8c8f094a722df15c93c/transformed/media3-exoplayer-1.4.1/proguard.txt
# Proguard rules specific to the core module.

# Constructors accessed via reflection in DefaultRenderersFactory
-dontnote androidx.media3.decoder.vp9.LibvpxVideoRenderer
-keepclassmembers class androidx.media3.decoder.vp9.LibvpxVideoRenderer {
  <init>(long, android.os.Handler, androidx.media3.exoplayer.video.VideoRendererEventListener, int);
}
-dontnote androidx.media3.decoder.av1.Libgav1VideoRenderer
-keepclassmembers class androidx.media3.decoder.av1.Libgav1VideoRenderer {
  <init>(long, android.os.Handler, androidx.media3.exoplayer.video.VideoRendererEventListener, int);
}
-dontnote androidx.media3.decoder.ffmpeg.ExperimentalFfmpegVideoRenderer
-keepclassmembers class androidx.media3.decoder.ffmpeg.ExperimentalFfmpegVideoRenderer {
  <init>(long, android.os.Handler, androidx.media3.exoplayer.video.VideoRendererEventListener, int);
}
-dontnote androidx.media3.decoder.opus.LibopusAudioRenderer
-keepclassmembers class androidx.media3.decoder.opus.LibopusAudioRenderer {
  <init>(android.os.Handler, androidx.media3.exoplayer.audio.AudioRendererEventListener, androidx.media3.exoplayer.audio.AudioSink);
}
-dontnote androidx.media3.decoder.flac.LibflacAudioRenderer
-keepclassmembers class androidx.media3.decoder.flac.LibflacAudioRenderer {
  <init>(android.os.Handler, androidx.media3.exoplayer.audio.AudioRendererEventListener, androidx.media3.exoplayer.audio.AudioSink);
}
-dontnote androidx.media3.decoder.ffmpeg.FfmpegAudioRenderer
-keepclassmembers class androidx.media3.decoder.ffmpeg.FfmpegAudioRenderer {
  <init>(android.os.Handler, androidx.media3.exoplayer.audio.AudioRendererEventListener, androidx.media3.exoplayer.audio.AudioSink);
}
-dontnote androidx.media3.decoder.midi.MidiRenderer
-keepclassmembers class androidx.media3.decoder.midi.MidiRenderer {
  <init>(android.content.Context);
}

# Constructors accessed via reflection in DefaultDownloaderFactory
-dontnote androidx.media3.exoplayer.dash.offline.DashDownloader
-keepclassmembers class androidx.media3.exoplayer.dash.offline.DashDownloader {
  <init>(androidx.media3.common.MediaItem, androidx.media3.datasource.cache.CacheDataSource$Factory, java.util.concurrent.Executor);
}
-dontnote androidx.media3.exoplayer.hls.offline.HlsDownloader
-keepclassmembers class androidx.media3.exoplayer.hls.offline.HlsDownloader {
  <init>(androidx.media3.common.MediaItem, androidx.media3.datasource.cache.CacheDataSource$Factory, java.util.concurrent.Executor);
}
-dontnote androidx.media3.exoplayer.smoothstreaming.offline.SsDownloader
-keepclassmembers class androidx.media3.exoplayer.smoothstreaming.offline.SsDownloader {
  <init>(androidx.media3.common.MediaItem, androidx.media3.datasource.cache.CacheDataSource$Factory, java.util.concurrent.Executor);
}

# Constructors accessed via reflection in DefaultMediaSourceFactory
-dontnote androidx.media3.exoplayer.dash.DashMediaSource$Factory
-keepclasseswithmembers class androidx.media3.exoplayer.dash.DashMediaSource$Factory {
  <init>(androidx.media3.datasource.DataSource$Factory);
}
-dontnote androidx.media3.exoplayer.hls.HlsMediaSource$Factory
-keepclasseswithmembers class androidx.media3.exoplayer.hls.HlsMediaSource$Factory {
  <init>(androidx.media3.datasource.DataSource$Factory);
}
-dontnote androidx.media3.exoplayer.smoothstreaming.SsMediaSource$Factory
-keepclasseswithmembers class androidx.media3.exoplayer.smoothstreaming.SsMediaSource$Factory {
  <init>(androidx.media3.datasource.DataSource$Factory);
}
-dontnote androidx.media3.exoplayer.rtsp.RtspMediaSource$Factory
-keepclasseswithmembers class androidx.media3.exoplayer.rtsp.RtspMediaSource$Factory {
  <init>();
}

# Constructors and methods accessed via reflection in CompositingVideoSinkProvider
-dontnote androidx.media3.effect.PreviewingSingleInputVideoGraph$Factory
-keepclasseswithmembers class androidx.media3.effect.PreviewingSingleInputVideoGraph$Factory {
  <init>(androidx.media3.common.VideoFrameProcessor$Factory);
}
-dontnote androidx.media3.effect.DefaultVideoFrameProcessor$Factory$Builder
-keepclasseswithmembers class androidx.media3.effect.DefaultVideoFrameProcessor$Factory$Builder {
  androidx.media3.effect.DefaultVideoFrameProcessor$Factory build();
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/e0b353b8a8ecb8c8f094a722df15c93c/transformed/media3-exoplayer-1.4.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/3dda310a8ed85dc3ca21fb8b0fffc6d4/transformed/webkit-1.12.1/proguard.txt
# Copyright 2018 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# We need to avoid obfuscating the support library boundary interface because
# this API is shared with the Android Support Library.
# Note that we only 'keep' the package org.chromium.support_lib_boundary itself,
# any sub-packages of that package can still be obfuscated.
-keep public class org.chromium.support_lib_boundary.* { public *; }

# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Prevent WebViewClientCompat from being renamed, since chromium depends on this name.
-keepnames public class androidx.webkit.WebViewClientCompat

# Prevent ProcessGlobalConfig and member sProcessGlobalConfig from being renamed, since chromium
# depends on this name.
-keepnames public class androidx.webkit.ProcessGlobalConfig {
    private static final *** sProcessGlobalConfig;
}
# End of content from /Users/<USER>/.gradle/caches/transforms-3/3dda310a8ed85dc3ca21fb8b0fffc6d4/transformed/webkit-1.12.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/e1432422334461c5abf599b8dbddd270/transformed/media-1.7.0/proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Prevent Parcelable objects from being removed or renamed.
-keep class android.support.v4.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Prevent Parcelable objects from being removed or renamed.
-keep class androidx.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}
# End of content from /Users/<USER>/.gradle/caches/transforms-3/e1432422334461c5abf599b8dbddd270/transformed/media-1.7.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/573cde57e17d30d829a1457e03438b3b/transformed/coordinatorlayout-1.1.0/proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# CoordinatorLayout resolves the behaviors of its child components with reflection.
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>();
}

# Make sure we keep annotations for CoordinatorLayout's DefaultBehavior and ViewPager's DecorView
-keepattributes *Annotation*

# End of content from /Users/<USER>/.gradle/caches/transforms-3/573cde57e17d30d829a1457e03438b3b/transformed/coordinatorlayout-1.1.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/7c7209f139f051b6f880ed68f5a57e51/transformed/vectordrawable-animated-1.1.0/proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# keep setters in VectorDrawables so that animations can still work.
-keepclassmembers class androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$* {
   void set*(***);
   *** get*();
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/7c7209f139f051b6f880ed68f5a57e51/transformed/vectordrawable-animated-1.1.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/f33cf5ea82bedd4794279fb2563bbcfd/transformed/recyclerview-1.1.0/proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# When layoutManager xml attribute is used, RecyclerView inflates
#LayoutManagers' constructors using reflection.
-keep public class * extends androidx.recyclerview.widget.RecyclerView$LayoutManager {
    public <init>(android.content.Context, android.util.AttributeSet, int, int);
    public <init>();
}

-keepclassmembers class androidx.recyclerview.widget.RecyclerView {
    public void suppressLayout(boolean);
    public boolean isLayoutSuppressed();
}
# End of content from /Users/<USER>/.gradle/caches/transforms-3/f33cf5ea82bedd4794279fb2563bbcfd/transformed/recyclerview-1.1.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/proguard.txt
# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.ViewCompat$Api* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.WindowInsetsCompat$*Impl* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.app.NotificationCompat$*$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.os.UserHandleCompat$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.widget.EdgeEffectCompat$Api*Impl {
  <methods>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/48b2b5e224a2f110a57147aa68b2a2c6/transformed/room-runtime-2.6.1/proguard.txt
-keep class * extends androidx.room.RoomDatabase
-dontwarn androidx.room.paging.**
-dontwarn androidx.lifecycle.LiveData

# End of content from /Users/<USER>/.gradle/caches/transforms-3/48b2b5e224a2f110a57147aa68b2a2c6/transformed/room-runtime-2.6.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/74d7dc1ea05e75c14cbbc4fa857be6ee/transformed/rules/lib/META-INF/proguard/okio.pro
# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*

# End of content from /Users/<USER>/.gradle/caches/transforms-3/74d7dc1ea05e75c14cbbc4fa857be6ee/transformed/rules/lib/META-INF/proguard/okio.pro
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/41f3e9c6c35e5f3d8dc94e9b17296868/transformed/rules/lib/META-INF/proguard/protobuf.pro
# Recently Protobuf Javalite introduced a change that relies on reflection,
# which doesn't work with Proguard. This rule keeps the reflection usages in
# (shaded) Protobuf classes in Tink as-is.
# The location of this file is determined by
# - https://developer.android.com/studio/build/shrink-code#configuration-files
# - https://docs.bazel.build/versions/master/be/java.html#java_library.resources
# See also:
# - https://github.com/google/tink/issues/361
# - https://github.com/protocolbuffers/protobuf/issues/6463
# WARNING: the shaded package name com.google.crypto.tink.shaded.protobuf must
# be kept in sync with jar_jar_rules.txt.
-keepclassmembers class * extends com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite {
  <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/41f3e9c6c35e5f3d8dc94e9b17296868/transformed/rules/lib/META-INF/proguard/protobuf.pro
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/dbb2724e4d71d30ed9011fddbb54a8a2/transformed/firebase-components-18.0.0/proguard.txt
-dontwarn com.google.firebase.components.Component$Instantiation
-dontwarn com.google.firebase.components.Component$ComponentType

-keep class * implements com.google.firebase.components.ComponentRegistrar
-keep,allowshrinking interface com.google.firebase.components.ComponentRegistrar

# End of content from /Users/<USER>/.gradle/caches/transforms-3/dbb2724e4d71d30ed9011fddbb54a8a2/transformed/firebase-components-18.0.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/a7a8e61d0ac5ab0e055aa6cb087c14e4/transformed/startup-runtime-1.1.1/proguard.txt
# It's important that we preserve initializer names, given they are used in the AndroidManifest.xml.
-keepnames class * extends androidx.startup.Initializer

# These Proguard rules ensures that ComponentInitializers are are neither shrunk nor obfuscated,
# and are a part of the primary dex file. This is because they are discovered and instantiated
# during application startup.
-keep class * extends androidx.startup.Initializer {
    # Keep the public no-argument constructor while allowing other methods to be optimized.
    <init>();
}

-assumenosideeffects class androidx.startup.StartupLogger { public static <methods>; }

# End of content from /Users/<USER>/.gradle/caches/transforms-3/a7a8e61d0ac5ab0e055aa6cb087c14e4/transformed/startup-runtime-1.1.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/d2a074df3f50ee54bbde0b09e0de1067/transformed/versionedparcelable-1.1.1/proguard.txt
-keep class * implements androidx.versionedparcelable.VersionedParcelable
-keep public class android.support.**Parcelizer { *; }
-keep public class androidx.**Parcelizer { *; }
-keep public class androidx.versionedparcelable.ParcelImpl

# End of content from /Users/<USER>/.gradle/caches/transforms-3/d2a074df3f50ee54bbde0b09e0de1067/transformed/versionedparcelable-1.1.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from /Users/<USER>/.gradle/caches/transforms-3/8df3a2ecdb00b38004a2a054873283f6/transformed/transport-backend-cct-3.1.9/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/709a26a8a6ca23234da41827a47e3a52/transformed/firebase-encoders-json-18.0.0/proguard.txt

# End of content from /Users/<USER>/.gradle/caches/transforms-3/709a26a8a6ca23234da41827a47e3a52/transformed/firebase-encoders-json-18.0.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/2c94fb9cbc8b6ff89f502efe195a5918/transformed/transport-api-3.1.0/proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from /Users/<USER>/.gradle/caches/transforms-3/2c94fb9cbc8b6ff89f502efe195a5918/transformed/transport-api-3.1.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/f33e7f7b42507cae78e6d9a0a6a403c0/transformed/core-1.0.0/proguard.txt
# Copyright (C) 2022 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# These interfaces must be kept for the client library to invoke methods in extensions.
-keep interface androidx.window.extensions.core.util.function.Consumer {
  public void accept(***);
}
-keep interface androidx.window.extensions.core.util.function.Predicate {
  public boolean test(***);
}
-keep interface androidx.window.extensions.core.util.function.Function {
  public *** apply(***);
}
# End of content from /Users/<USER>/.gradle/caches/transforms-3/f33e7f7b42507cae78e6d9a0a6a403c0/transformed/core-1.0.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/4e6fdbd7b75184a7919dfe5ef7596cce/transformed/rules/lib/META-INF/proguard/androidx-annotations.pro
-keep,allowobfuscation @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

-keepclassmembers,allowobfuscation class * {
  @androidx.annotation.DoNotInline <methods>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/4e6fdbd7b75184a7919dfe5ef7596cce/transformed/rules/lib/META-INF/proguard/androidx-annotations.pro
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/proguard.txt
#https://github.com/leolin310148/ShortcutBadger/issues/46
-keep class me.leolin.shortcutbadger.impl.AdwHomeBadger { <init>(...); }
-keep class me.leolin.shortcutbadger.impl.ApexHomeBadger { <init>(...); }
-keep class me.leolin.shortcutbadger.impl.AsusHomeLauncher { <init>(...); }
-keep class me.leolin.shortcutbadger.impl.DefaultBadger { <init>(...); }
-keep class me.leolin.shortcutbadger.impl.NewHtcHomeBadger { <init>(...); }
-keep class me.leolin.shortcutbadger.impl.NovaHomeBadger { <init>(...); }
-keep class me.leolin.shortcutbadger.impl.SolidHomeBadger { <init>(...); }
-keep class me.leolin.shortcutbadger.impl.SonyHomeBadger { <init>(...); }
-keep class me.leolin.shortcutbadger.impl.XiaomiHomeBadger { <init>(...); }
# End of content from /Users/<USER>/.gradle/caches/transforms-3/6214383cef58ff8d23c031d85ebefb5c/transformed/ShortcutBadger-1.1.22/proguard.txt
# The proguard configuration file for the following section is <unknown>

# End of content from <unknown>