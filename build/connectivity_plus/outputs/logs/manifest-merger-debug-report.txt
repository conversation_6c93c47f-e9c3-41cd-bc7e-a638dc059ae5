-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/android/src/main/AndroidManifest.xml:1:1-4:12
INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/android/src/main/AndroidManifest.xml:1:1-4:12
	package
		ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/android/src/main/AndroidManifest.xml:2:3-51
		INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/android/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/android/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/android/src/main/AndroidManifest.xml:3:3-76
	android:name
		ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/android/src/main/AndroidManifest.xml:3:20-74
uses-sdk
INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/android/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/android/src/main/AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/android/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/android/src/main/AndroidManifest.xml
