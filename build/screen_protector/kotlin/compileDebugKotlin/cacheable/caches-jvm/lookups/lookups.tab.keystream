  Activity android.app  window android.app.Activity  Window android.view  
WindowManager android.view  
clearFlags android.view.Window  setFlags android.view.Window  FLAG_SECURE 'android.view.WindowManager.LayoutParams  Activity com.prongbang.screen_protector  
ActivityAware com.prongbang.screen_protector  ActivityPluginBinding com.prongbang.screen_protector  	Exception com.prongbang.screen_protector  
FlutterPlugin com.prongbang.screen_protector  
MethodCall com.prongbang.screen_protector  MethodCallHandler com.prongbang.screen_protector  
MethodChannel com.prongbang.screen_protector  ScreenProtectorPlugin com.prongbang.screen_protector  
WindowManager com.prongbang.screen_protector  FlutterPluginBinding ,com.prongbang.screen_protector.FlutterPlugin  Result ,com.prongbang.screen_protector.MethodChannel  
MethodChannel 4com.prongbang.screen_protector.ScreenProtectorPlugin  
WindowManager 4com.prongbang.screen_protector.ScreenProtectorPlugin  activity 4com.prongbang.screen_protector.ScreenProtectorPlugin  channel 4com.prongbang.screen_protector.ScreenProtectorPlugin  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  success -io.flutter.plugin.common.MethodChannel.Result  	Exception 	java.lang                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       