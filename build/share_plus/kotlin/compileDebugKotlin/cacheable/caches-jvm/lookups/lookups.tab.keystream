  Activity android.app  
PendingIntent android.app  
startActivity android.app.Activity  startActivityForResult android.app.Activity  FLAG_MUTABLE android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  getBroadcast android.app.PendingIntent  intentSender android.app.PendingIntent  
ACTIVITY_CODE android.content  ActivityResultListener android.content  
AtomicBoolean android.content  Boolean android.content  BroadcastReceiver android.content  Build android.content  
ComponentName android.content  Context android.content  Int android.content  Intent android.content  IntentSender android.content  
MethodChannel android.content  RESULT_UNAVAILABLE android.content  SharePlusPendingIntent android.content  String android.content  java android.content  result android.content  Build !android.content.BroadcastReceiver  
ComponentName !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  java !android.content.BroadcastReceiver  result !android.content.BroadcastReceiver  flattenToString android.content.ComponentName  cacheDir android.content.Context  grantUriPermission android.content.Context  packageManager android.content.Context  packageName android.content.Context  
startActivity android.content.Context  ACTION_SEND android.content.Intent  ACTION_SEND_MULTIPLE android.content.Intent  EXTRA_CHOSEN_COMPONENT android.content.Intent  EXTRA_STREAM android.content.Intent  
EXTRA_SUBJECT android.content.Intent  
EXTRA_TEXT android.content.Intent  FLAG_ACTIVITY_NEW_TASK android.content.Intent  FLAG_GRANT_READ_URI_PERMISSION android.content.Intent  FLAG_GRANT_WRITE_URI_PERMISSION android.content.Intent  Intent android.content.Intent  action android.content.Intent  addFlags android.content.Intent  apply android.content.Intent  
createChooser android.content.Intent  first android.content.Intent  getParcelableExtra android.content.Intent  putExtra android.content.Intent  putParcelableArrayListExtra android.content.Intent  reduceMimeTypes android.content.Intent  type android.content.Intent  Result android.content.MethodChannel  PackageManager android.content.pm  packageName "android.content.pm.PackageItemInfo  MATCH_DEFAULT_ONLY !android.content.pm.PackageManager  queryIntentActivities !android.content.pm.PackageManager  activityInfo android.content.pm.ResolveInfo  Uri android.net  Build 
android.os  SDK_INT android.os.Build.VERSION  LOLLIPOP_MR1 android.os.Build.VERSION_CODES  M android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  FileProvider androidx.core.content  
getUriForFile "androidx.core.content.FileProvider  
ACTIVITY_CODE dev.fluttercommunity.plus.share  Activity dev.fluttercommunity.plus.share  
ActivityAware dev.fluttercommunity.plus.share  ActivityPluginBinding dev.fluttercommunity.plus.share  ActivityResultListener dev.fluttercommunity.plus.share  Any dev.fluttercommunity.plus.share  	ArrayList dev.fluttercommunity.plus.share  
AtomicBoolean dev.fluttercommunity.plus.share  Boolean dev.fluttercommunity.plus.share  BroadcastReceiver dev.fluttercommunity.plus.share  Build dev.fluttercommunity.plus.share  CHANNEL dev.fluttercommunity.plus.share  
ComponentName dev.fluttercommunity.plus.share  Context dev.fluttercommunity.plus.share  File dev.fluttercommunity.plus.share  FileProvider dev.fluttercommunity.plus.share  
FlutterPlugin dev.fluttercommunity.plus.share  FlutterPluginBinding dev.fluttercommunity.plus.share  IOException dev.fluttercommunity.plus.share  IllegalArgumentException dev.fluttercommunity.plus.share  Int dev.fluttercommunity.plus.share  Intent dev.fluttercommunity.plus.share  List dev.fluttercommunity.plus.share  Map dev.fluttercommunity.plus.share  
MethodCall dev.fluttercommunity.plus.share  MethodCallHandler dev.fluttercommunity.plus.share  
MethodChannel dev.fluttercommunity.plus.share  PackageManager dev.fluttercommunity.plus.share  
PendingIntent dev.fluttercommunity.plus.share  RESULT_UNAVAILABLE dev.fluttercommunity.plus.share  Share dev.fluttercommunity.plus.share  ShareFileProvider dev.fluttercommunity.plus.share  SharePlusPendingIntent dev.fluttercommunity.plus.share  SharePlusPlugin dev.fluttercommunity.plus.share  ShareSuccessManager dev.fluttercommunity.plus.share  String dev.fluttercommunity.plus.share  Throws dev.fluttercommunity.plus.share  Uri dev.fluttercommunity.plus.share  apply dev.fluttercommunity.plus.share  contains dev.fluttercommunity.plus.share  copyTo dev.fluttercommunity.plus.share  endsWith dev.fluttercommunity.plus.share  first dev.fluttercommunity.plus.share  forEach dev.fluttercommunity.plus.share  getValue dev.fluttercommunity.plus.share  indexOf dev.fluttercommunity.plus.share  
isNullOrBlank dev.fluttercommunity.plus.share  
isNullOrEmpty dev.fluttercommunity.plus.share  java dev.fluttercommunity.plus.share  	lastIndex dev.fluttercommunity.plus.share  lazy dev.fluttercommunity.plus.share  provideDelegate dev.fluttercommunity.plus.share  reduceMimeTypes dev.fluttercommunity.plus.share  require dev.fluttercommunity.plus.share  result dev.fluttercommunity.plus.share  
startsWith dev.fluttercommunity.plus.share  	substring dev.fluttercommunity.plus.share  Build 1dev.fluttercommunity.plus.share.MethodCallHandler  IllegalArgumentException 1dev.fluttercommunity.plus.share.MethodCallHandler  endsWith 1dev.fluttercommunity.plus.share.MethodCallHandler  expectMapArguments 1dev.fluttercommunity.plus.share.MethodCallHandler  manager 1dev.fluttercommunity.plus.share.MethodCallHandler  require 1dev.fluttercommunity.plus.share.MethodCallHandler  share 1dev.fluttercommunity.plus.share.MethodCallHandler  MethodCallHandler -dev.fluttercommunity.plus.share.MethodChannel  Result -dev.fluttercommunity.plus.share.MethodChannel  	ArrayList %dev.fluttercommunity.plus.share.Share  Build %dev.fluttercommunity.plus.share.Share  File %dev.fluttercommunity.plus.share.Share  FileProvider %dev.fluttercommunity.plus.share.Share  IOException %dev.fluttercommunity.plus.share.Share  Intent %dev.fluttercommunity.plus.share.Share  PackageManager %dev.fluttercommunity.plus.share.Share  
PendingIntent %dev.fluttercommunity.plus.share.Share  SharePlusPendingIntent %dev.fluttercommunity.plus.share.Share  ShareSuccessManager %dev.fluttercommunity.plus.share.Share  activity %dev.fluttercommunity.plus.share.Share  apply %dev.fluttercommunity.plus.share.Share  clearShareCacheFolder %dev.fluttercommunity.plus.share.Share  contains %dev.fluttercommunity.plus.share.Share  context %dev.fluttercommunity.plus.share.Share  copyTo %dev.fluttercommunity.plus.share.Share  copyToShareCacheFolder %dev.fluttercommunity.plus.share.Share  fileIsInShareCache %dev.fluttercommunity.plus.share.Share  first %dev.fluttercommunity.plus.share.Share  forEach %dev.fluttercommunity.plus.share.Share  
getContext %dev.fluttercommunity.plus.share.Share  getMimeTypeBase %dev.fluttercommunity.plus.share.Share  getUrisForPaths %dev.fluttercommunity.plus.share.Share  getValue %dev.fluttercommunity.plus.share.Share  immutabilityIntentFlags %dev.fluttercommunity.plus.share.Share  indexOf %dev.fluttercommunity.plus.share.Share  
isNullOrBlank %dev.fluttercommunity.plus.share.Share  
isNullOrEmpty %dev.fluttercommunity.plus.share.Share  java %dev.fluttercommunity.plus.share.Share  	lastIndex %dev.fluttercommunity.plus.share.Share  lazy %dev.fluttercommunity.plus.share.Share  manager %dev.fluttercommunity.plus.share.Share  provideDelegate %dev.fluttercommunity.plus.share.Share  providerAuthority %dev.fluttercommunity.plus.share.Share  reduceMimeTypes %dev.fluttercommunity.plus.share.Share  setActivity %dev.fluttercommunity.plus.share.Share  share %dev.fluttercommunity.plus.share.Share  shareCacheFolder %dev.fluttercommunity.plus.share.Share  
shareFiles %dev.fluttercommunity.plus.share.Share  
startActivity %dev.fluttercommunity.plus.share.Share  
startsWith %dev.fluttercommunity.plus.share.Share  	substring %dev.fluttercommunity.plus.share.Share  Build 6dev.fluttercommunity.plus.share.SharePlusPendingIntent  	Companion 6dev.fluttercommunity.plus.share.SharePlusPendingIntent  
ComponentName 6dev.fluttercommunity.plus.share.SharePlusPendingIntent  Context 6dev.fluttercommunity.plus.share.SharePlusPendingIntent  Intent 6dev.fluttercommunity.plus.share.SharePlusPendingIntent  String 6dev.fluttercommunity.plus.share.SharePlusPendingIntent  java 6dev.fluttercommunity.plus.share.SharePlusPendingIntent  result 6dev.fluttercommunity.plus.share.SharePlusPendingIntent  Build @dev.fluttercommunity.plus.share.SharePlusPendingIntent.Companion  
ComponentName @dev.fluttercommunity.plus.share.SharePlusPendingIntent.Companion  Intent @dev.fluttercommunity.plus.share.SharePlusPendingIntent.Companion  java @dev.fluttercommunity.plus.share.SharePlusPendingIntent.Companion  result @dev.fluttercommunity.plus.share.SharePlusPendingIntent.Companion  ActivityPluginBinding /dev.fluttercommunity.plus.share.SharePlusPlugin  CHANNEL /dev.fluttercommunity.plus.share.SharePlusPlugin  FlutterPluginBinding /dev.fluttercommunity.plus.share.SharePlusPlugin  MethodCallHandler /dev.fluttercommunity.plus.share.SharePlusPlugin  
MethodChannel /dev.fluttercommunity.plus.share.SharePlusPlugin  Share /dev.fluttercommunity.plus.share.SharePlusPlugin  ShareSuccessManager /dev.fluttercommunity.plus.share.SharePlusPlugin  manager /dev.fluttercommunity.plus.share.SharePlusPlugin  
methodChannel /dev.fluttercommunity.plus.share.SharePlusPlugin  onAttachedToActivity /dev.fluttercommunity.plus.share.SharePlusPlugin  onDetachedFromActivity /dev.fluttercommunity.plus.share.SharePlusPlugin  share /dev.fluttercommunity.plus.share.SharePlusPlugin  CHANNEL 9dev.fluttercommunity.plus.share.SharePlusPlugin.Companion  MethodCallHandler 9dev.fluttercommunity.plus.share.SharePlusPlugin.Companion  
MethodChannel 9dev.fluttercommunity.plus.share.SharePlusPlugin.Companion  Share 9dev.fluttercommunity.plus.share.SharePlusPlugin.Companion  ShareSuccessManager 9dev.fluttercommunity.plus.share.SharePlusPlugin.Companion  
ACTIVITY_CODE 3dev.fluttercommunity.plus.share.ShareSuccessManager  
AtomicBoolean 3dev.fluttercommunity.plus.share.ShareSuccessManager  Boolean 3dev.fluttercommunity.plus.share.ShareSuccessManager  	Companion 3dev.fluttercommunity.plus.share.ShareSuccessManager  Context 3dev.fluttercommunity.plus.share.ShareSuccessManager  Int 3dev.fluttercommunity.plus.share.ShareSuccessManager  Intent 3dev.fluttercommunity.plus.share.ShareSuccessManager  
MethodChannel 3dev.fluttercommunity.plus.share.ShareSuccessManager  RESULT_UNAVAILABLE 3dev.fluttercommunity.plus.share.ShareSuccessManager  SharePlusPendingIntent 3dev.fluttercommunity.plus.share.ShareSuccessManager  String 3dev.fluttercommunity.plus.share.ShareSuccessManager  callback 3dev.fluttercommunity.plus.share.ShareSuccessManager  isCalledBack 3dev.fluttercommunity.plus.share.ShareSuccessManager  returnResult 3dev.fluttercommunity.plus.share.ShareSuccessManager  setCallback 3dev.fluttercommunity.plus.share.ShareSuccessManager  unavailable 3dev.fluttercommunity.plus.share.ShareSuccessManager  
ACTIVITY_CODE =dev.fluttercommunity.plus.share.ShareSuccessManager.Companion  
AtomicBoolean =dev.fluttercommunity.plus.share.ShareSuccessManager.Companion  RESULT_UNAVAILABLE =dev.fluttercommunity.plus.share.ShareSuccessManager.Companion  SharePlusPendingIntent =dev.fluttercommunity.plus.share.ShareSuccessManager.Companion  Result Adev.fluttercommunity.plus.share.ShareSuccessManager.MethodChannel  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  addActivityResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  argument #io.flutter.plugin.common.MethodCall  	arguments #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  ActivityResultListener 'io.flutter.plugin.common.PluginRegistry  File java.io  IOException java.io  
canonicalPath java.io.File  copyTo java.io.File  delete java.io.File  exists java.io.File  	listFiles java.io.File  mkdirs java.io.File  name java.io.File  message java.io.IOException  Class 	java.lang  	ArrayList 	java.util  add java.util.ArrayList  first java.util.ArrayList  isEmpty java.util.ArrayList  size java.util.ArrayList  
AtomicBoolean java.util.concurrent.atomic  
compareAndSet )java.util.concurrent.atomic.AtomicBoolean  set )java.util.concurrent.atomic.AtomicBoolean  	Function0 kotlin  	Function1 kotlin  IllegalArgumentException kotlin  Lazy kotlin  Nothing kotlin  apply kotlin  getValue kotlin  lazy kotlin  require kotlin  not kotlin.Boolean  	compareTo 
kotlin.Int  or 
kotlin.Int  rangeTo 
kotlin.Int  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  contains 
kotlin.String  indexOf 
kotlin.String  
isNullOrBlank 
kotlin.String  plus 
kotlin.String  	substring 
kotlin.String  message kotlin.Throwable  IntIterator kotlin.collections  List kotlin.collections  Map kotlin.collections  contains kotlin.collections  first kotlin.collections  forEach kotlin.collections  getValue kotlin.collections  indexOf kotlin.collections  
isNullOrEmpty kotlin.collections  	lastIndex kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  first kotlin.collections.List  get kotlin.collections.List  isEmpty kotlin.collections.List  
isNullOrEmpty kotlin.collections.List  	lastIndex kotlin.collections.List  size kotlin.collections.List  copyTo 	kotlin.io  endsWith 	kotlin.io  
startsWith 	kotlin.io  Throws 
kotlin.jvm  java 
kotlin.jvm  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  contains 
kotlin.ranges  first 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  
KProperty1 kotlin.reflect  java kotlin.reflect.KClass  contains kotlin.sequences  first kotlin.sequences  forEach kotlin.sequences  indexOf kotlin.sequences  contains kotlin.text  endsWith kotlin.text  first kotlin.text  forEach kotlin.text  indexOf kotlin.text  
isNullOrBlank kotlin.text  
isNullOrEmpty kotlin.text  	lastIndex kotlin.text  
startsWith kotlin.text  	substring kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               