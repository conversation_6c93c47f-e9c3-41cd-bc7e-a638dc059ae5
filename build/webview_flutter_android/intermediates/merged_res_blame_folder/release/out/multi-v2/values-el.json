{"logs": [{"outputFile": "io.flutter.plugins.webviewflutter.webview_flutter_android-mergeReleaseResources-24:/values-el/values-el.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/res/values-el/values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}}]}, {"outputFile": "io.flutter.plugins.webviewflutter.webview_flutter_android-merged_res-26:/values-el/values-el.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/res/values-el/values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}}]}]}