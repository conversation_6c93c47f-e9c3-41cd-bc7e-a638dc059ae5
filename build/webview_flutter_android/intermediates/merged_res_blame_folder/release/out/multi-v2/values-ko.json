{"logs": [{"outputFile": "io.flutter.plugins.webviewflutter.webview_flutter_android-merged_res-26:/values-ko/values-ko.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/res/values-ko/values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}}]}, {"outputFile": "io.flutter.plugins.webviewflutter.webview_flutter_android-mergeReleaseResources-24:/values-ko/values-ko.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/res/values-ko/values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}}]}]}