{"logs": [{"outputFile": "io.flutter.plugins.webviewflutter.webview_flutter_android-merged_res-26:/values-te/values-te.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/res/values-te/values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}}]}, {"outputFile": "io.flutter.plugins.webviewflutter.webview_flutter_android-mergeReleaseResources-24:/values-te/values-te.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/92d7edc3c098947527172665a998116a/transformed/core-1.13.1/res/values-te/values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}}]}]}