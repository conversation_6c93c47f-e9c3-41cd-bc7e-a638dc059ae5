[{"merged": "io.flutter.plugins.webviewflutter.webview_flutter_android-merged_res-26:/animator/fragment_open_exit.xml", "source": "io.flutter.plugins.webviewflutter.webview_flutter_android-fragment-1.7.1-7:/animator/fragment_open_exit.xml"}, {"merged": "io.flutter.plugins.webviewflutter.webview_flutter_android-merged_res-26:/animator/fragment_close_enter.xml", "source": "io.flutter.plugins.webviewflutter.webview_flutter_android-fragment-1.7.1-7:/animator/fragment_close_enter.xml"}, {"merged": "io.flutter.plugins.webviewflutter.webview_flutter_android-merged_res-26:/animator/fragment_fade_exit.xml", "source": "io.flutter.plugins.webviewflutter.webview_flutter_android-fragment-1.7.1-7:/animator/fragment_fade_exit.xml"}, {"merged": "io.flutter.plugins.webviewflutter.webview_flutter_android-merged_res-26:/animator/fragment_open_enter.xml", "source": "io.flutter.plugins.webviewflutter.webview_flutter_android-fragment-1.7.1-7:/animator/fragment_open_enter.xml"}, {"merged": "io.flutter.plugins.webviewflutter.webview_flutter_android-merged_res-26:/animator/fragment_close_exit.xml", "source": "io.flutter.plugins.webviewflutter.webview_flutter_android-fragment-1.7.1-7:/animator/fragment_close_exit.xml"}, {"merged": "io.flutter.plugins.webviewflutter.webview_flutter_android-merged_res-26:/animator/fragment_fade_enter.xml", "source": "io.flutter.plugins.webviewflutter.webview_flutter_android-fragment-1.7.1-7:/animator/fragment_fade_enter.xml"}]