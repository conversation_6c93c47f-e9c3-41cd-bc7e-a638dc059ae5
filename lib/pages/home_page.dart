import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart' as provider;
import '../models/course.dart';
import '../models/course_video.dart';
import '../services/api_service.dart';
import '../models/user_profile.dart';
import '../services/user_service.dart';
import '../utils/animations.dart';
import '../theme/app_theme.dart';
import '../pages/course_videos_page.dart';
import '../pages/calorie_tracker_page.dart';
import '../design_system/kft_design_system.dart';
import '../models/calorie_log.dart';
import '../widgets/theme_toggle_button.dart';
import 'dart:ui';
import 'dart:math' as math;
import '../services/quote_service.dart';
import '../models/motivational_quote.dart';
import '../widgets/daily_motivational_quote.dart';
import '../widgets/simple_notification_icon.dart';
import '../widgets/profile_avatar_enhanced.dart';
import '../widgets/enhanced_whatsapp_support_fab.dart';
import '../widgets/staff_name_animation_widget.dart';
import 'progress_page_new.dart';
import 'dedicated_video_player_page.dart';
import 'comprehensive_video_player_page.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import '../providers/auth_provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../pages/nutrition_guide_page.dart';
import '../models/nutrition_story_fixed.dart';
import '../widgets/home_stories_widget.dart';
import '../widgets/story_widgets.dart';
import '../services/course_tracking_service.dart';
import '../widgets/daily_streak_ring.dart';
import '../services/video_streak_service.dart';
import 'package:intl/intl.dart';
import '../widgets/optimized_image_widget.dart';
import '../widgets/skeleton_widgets.dart';
import '../services/performance_monitor.dart';
import '../widgets/hydration_home_widget.dart';
import '../providers/water_reminder_provider.dart';
import '../widgets/hydrated_progress_widgets.dart';
import '../services/progress_service.dart';
import '../services/daily_streak_service.dart';

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with SingleTickerProviderStateMixin {
  final ApiService _apiService = ApiService();
  final UserService _userService = UserService();
  final PerformanceMonitor _performanceMonitor = PerformanceMonitor();

  bool _isLoadingProfile = true;
  bool _isLoadingCourses = true;
  bool _isLoadingQuote = true;
  bool _isLoadingCourseVideos = true;
  String _errorMessage = '';
  List<Course> _courses = [];
  UserProfile? _profile;

  // Store course videos for the Continue Watching section
  Map<int, List<CourseVideo>> _courseVideos = {};

  // ScrollController for the app bar and body
  final ScrollController _scrollController = ScrollController();

  // For animations
  late AnimationController _animationController;

  // For search functionality
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  // For time-based greeting
  String get _greeting {
    final hour = DateTime.now().hour;
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  }

  // Motivational quotes
  final List<String> _motivationalQuotes = [
    "The only bad workout is the one that didn't happen.",
    "Fitness is not about being better than someone else. It's about being better than you used to be.",
    "Take care of your body. It's the only place you have to live.",
    "The hard days are the best because that's when champions are made.",
    "Strive for progress, not perfection.",
  ];

  bool _deepSeekEnabled = false;
  MotivationalQuote? _quote;

  int _currentStreak = 0;
  int _highestStreak = 0;
  int _nextMilestone = 7;
  bool _isMilestone = false;
  bool _hasShownMilestone = false;

  // Staff animation state
  bool _showStaffAnimation = true;
  bool _staffDataLoaded = false;

  void _onStaffDataLoaded() {
    if (mounted) {
      setState(() {
        _staffDataLoaded = true;
      });
    }
  }

  void _onStaffAnimationComplete() {
    if (mounted) {
      setState(() {
        _showStaffAnimation = false;
      });
    }
  }

  void goToWorkout() {
    // Mock data for last played course and videos
    final lastCourse = _courses.isNotEmpty ? _courses.first : Course(
      id: 0,
      title: 'No Course',
      description: 'No course found.',
      thumbnailUrl: '',
      durationWeeks: 0,
      startDate: '',
      endDate: '',
      status: '',
      totalVideos: 0,
      unlockedVideos: 0,
      completedVideos: 0,
      progressPercentage: 0,
    );
    // Mock video list for the course
    final List<Map<String, dynamic>> courseVideos = [
      {'title': 'Warmup & Mobility', 'id': 1, 'unlocked': true},
      {'title': 'HIIT Session 1', 'id': 2, 'unlocked': true},
      {'title': 'HIIT Session 2', 'id': 3, 'unlocked': false},
      {'title': 'Cooldown & Stretch', 'id': 4, 'unlocked': false},
    ];
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      isScrollControlled: true,
      builder: (context) {
        return Padding(
          padding: MediaQuery.of(context).viewInsets,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Resume Workout',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                Text(
                  lastCourse.title,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 4),
                Text(
                  lastCourse.description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Theme.of(context).hintColor),
                ),
                const SizedBox(height: 16),
                Text(
                  'Course Videos',
                  style: Theme.of(context).textTheme.labelLarge,
                ),
                const SizedBox(height: 8),
                ...courseVideos.map((video) => ListTile(
                      leading: Icon(
                        video['unlocked'] ? Icons.play_arrow_rounded : Icons.lock_outline_rounded,
                        color: video['unlocked'] ? Theme.of(context).colorScheme.primary : Colors.grey,
                      ),
                      title: Text(
                        video['title'] as String,
                        style: TextStyle(
                          color: video['unlocked'] ? null : Colors.grey,
                        ),
                      ),
                      enabled: video['unlocked'],
                      onTap: video['unlocked']
                          ? () {
                              Navigator.pop(context);

                              // Create a mock CourseVideo for demonstration
                              final mockVideo = CourseVideo(
                                id: video['id'] as int,
                                title: video['title'] as String,
                                description: 'Video from ${lastCourse.title}',
                                videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4', // Sample video URL
                                weekNumber: 1,
                                sequenceNumber: 1,
                                isUnlocked: true,
                                isCompleted: false,
                                videoProvider: 'other',
                              );

                              // Open dedicated video player
                              _openDedicatedVideoPlayer(mockVideo);
                            }
                          : null,
                    )),
                const SizedBox(height: 8),
                Align(
                  alignment: Alignment.centerRight,
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.play_circle_fill_rounded),
                    label: const Text('Resume'),
                    onPressed: () {
                      // Mock: last watched video is the last unlocked video
                      final lastWatched = courseVideos.lastWhere(
                        (v) => v['unlocked'] == true,
                        orElse: () => <String, dynamic>{},
                      );
                      if (lastWatched.isNotEmpty) {
                        Navigator.pop(context);

                        // Create a mock CourseVideo for demonstration
                        final mockVideo = CourseVideo(
                          id: lastWatched['id'] as int,
                          title: lastWatched['title'] as String,
                          description: 'Resume video from ${lastCourse.title}',
                          videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4', // Sample video URL
                          weekNumber: 1,
                          sequenceNumber: 1,
                          isUnlocked: true,
                          isCompleted: false,
                          videoProvider: 'other',
                          lastPositionSeconds: 120, // Mock resume position
                        );

                        // Open dedicated video player
                        _openDedicatedVideoPlayer(mockVideo);
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void goToMeals() {
    // Navigate to the improved NutritionGuidePage with a slide transition
    // This page now includes comprehensive nutrition information, search functionality,
    // and detailed guides for proteins, carbs, fats, and fiber
    Navigator.push(
      context,
      SlidePageRoute(
        page: const NutritionGuidePage(),
        duration: const Duration(milliseconds: 300),
      ),
    );
  }

  void goToProgress() {
    // Navigate to the redesigned Progress page with the user profile
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProgressPageNew(user: _profile),
      ),
    ).then((_) {
      // Refresh data when returning from Progress page to ensure sync
      _loadAllData();
    });
  }

  void goToGoals() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Goals action triggered!')),
    );
  }

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _initStreak();
    _checkAuthAndLoadData();
  }

  Future<void> _checkAuthAndLoadData() async {
    try {
      // Check for token (auth)
      final token = await _apiService.getToken();
      if (token == null || token.isEmpty) {
        if (mounted) {
          Navigator.pushNamedAndRemoveUntil(context, '/login', (route) => false);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Session expired. Please log in again.'), backgroundColor: Colors.red),
          );
        }
        return;
      }
      await _loadAllData();
    } catch (e) {
      if (mounted) {
        final errorStr = e.toString().toLowerCase();
        if (errorStr.contains('401') ||
            errorStr.contains('session expired') ||
            errorStr.contains('not authenticated') ||
            errorStr.contains('device removed') ||
            errorStr.contains('device_mismatch')) {
          Navigator.pushNamedAndRemoveUntil(context, '/login', (route) => false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Session expired or device removed. Please log in again.'), backgroundColor: Colors.red),
          );
        } else if (errorStr.contains('socketexception') ||
                   errorStr.contains('failed host lookup') ||
                   errorStr.contains('network error') ||
                   errorStr.contains('timeout')) {
          setState(() {
            _errorMessage = 'No internet connection. Please check your network and try again.';
            _isLoadingProfile = false;
            _isLoadingCourses = false;
            _isLoadingQuote = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('No internet connection.'), backgroundColor: Colors.orange),
          );
        } else {
          setState(() {
            _errorMessage = 'An unexpected error occurred: $e';
            _isLoadingProfile = false;
            _isLoadingCourses = false;
            _isLoadingQuote = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('An error occurred: $e'), backgroundColor: Colors.red),
          );
        }
      }
    }
  }

  Future<void> _initStreak() async {
    try {
      print('🔥 Initializing homepage streak...');

      // Get the singleton instance
      final dailyStreakService = DailyStreakService();

      // Initialize the service first
      await dailyStreakService.initialize();

      // Update streak on app open (this handles the daily logic)
      await dailyStreakService.updateStreakOnAppOpen();

      // Update UI with current values
      if (mounted) {
        setState(() {
          _currentStreak = dailyStreakService.currentStreak;
          _highestStreak = dailyStreakService.highestStreak;
          _nextMilestone = dailyStreakService.nextMilestone();
          _isMilestone = dailyStreakService.isMilestone();
        });

        print('🔥 Homepage streak initialized - Current: $_currentStreak, Highest: $_highestStreak');
      }

      // Show milestone dialog if applicable
      if (_isMilestone && !_hasShownMilestone) {
        _hasShownMilestone = true;
        Future.delayed(const Duration(milliseconds: 600), () {
          if (mounted) {
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: const Row(
                  children: [
                    Icon(Icons.local_fire_department, color: Colors.deepOrangeAccent),
                    SizedBox(width: 8),
                    Text('Streak Milestone!'),
                  ],
                ),
                content: Text("Congrats! You've reached a $_currentStreak-day streak! Keep it up!"),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Awesome!'),
                  ),
                ],
              ),
            );
          }
        });
      }
    } catch (e) {
      print('❌ Error initializing homepage streak: $e');
      // Set default values on error
      if (mounted) {
        setState(() {
          _currentStreak = 0;
          _highestStreak = 0;
          _nextMilestone = 7;
          _isMilestone = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Optionally, re-check auth here if needed
  }

  // Load videos for a specific course
  Future<void> _loadCourseVideos(int courseId) async {
    try {
      final data = await _apiService.getCourseVideos(courseId);

      if (data['success'] == true && data['videos'] != null) {
        final List<CourseVideo> videos = [];
        final videosData = data['videos'];

        if (videosData is List) {
          for (var videoData in videosData) {
            try {
              if (videoData is Map<String, dynamic>) {
                videos.add(CourseVideo.fromJson(videoData));
              } else if (videoData is Map) {
                videos.add(CourseVideo.fromJson(Map<String, dynamic>.from(videoData)));
              }
            } catch (e) {
              print('Error parsing video data: $e');
              // Skip invalid video data instead of failing completely
            }
          }
        }

        if (!mounted) return;

        setState(() {
          _courseVideos[courseId] = videos;
        });
      } else {
        print('Failed to load videos for course $courseId: ${data['error'] ?? 'Unknown error'}');
        if (!mounted) return;

        setState(() {
          _courseVideos[courseId] = [];
        });
      }
    } catch (e) {
      print('Error loading videos for course $courseId: $e');
      // Silently handle error, as we already have the course info
      if (!mounted) return;

      setState(() {
        _courseVideos[courseId] = [];
      });
    }
  }

  // Load videos for all courses
  Future<void> _loadAllCourseVideos() async {
    if (_courses.isEmpty) return;

    setState(() {
      _isLoadingCourseVideos = true;
    });

    try {
      // Load videos for the first few courses (to avoid too many API calls)
      final coursesToLoad = _courses.take(3).toList();

      // Load videos in parallel
      await Future.wait(
        coursesToLoad.map((course) => _loadCourseVideos(course.id))
      );

      if (!mounted) return;

      setState(() {
        _isLoadingCourseVideos = false;
      });
    } catch (e) {
      print('Error loading course videos: $e');
      if (mounted) {
        setState(() {
          _isLoadingCourseVideos = false;
        });
      }
    }
  }

  Future<void> _loadAllData() async {
    _performanceMonitor.startOperation('homepage_data_load');

    setState(() {
      _isLoadingProfile = true;
      _isLoadingCourses = true;
      _isLoadingQuote = true;
      _isLoadingCourseVideos = true;
      _errorMessage = '';
    });
    try {
      // Fetch all in parallel
      final profileFuture = _userService.getUserProfile();
      final coursesFuture = _apiService.getUserCoursesAsList();
      final deepSeekFuture = QuoteService().getDeepSeekEnabledForUser();
      final results = await Future.wait([
        profileFuture,
        coursesFuture,
        deepSeekFuture,
      ]);
      final profile = results[0] as UserProfile;
      final courses = results[1] as List<Course>;
      final deepSeekEnabled = results[2] as bool;
      MotivationalQuote? quote;
      if (deepSeekEnabled) {
        try {
          quote = await QuoteService().generateAiQuote();
        } catch (_) {}
      }
      if (!mounted) return;
      setState(() {
        _profile = profile;
        _isLoadingProfile = false;
        _courses = courses;
        _isLoadingCourses = false;
        _deepSeekEnabled = deepSeekEnabled;
        _quote = quote;
        _isLoadingQuote = false;
        // Note: We use DailyStreakService for streak tracking, not UserProfile
        // The DailyStreakService provides more accurate "including today" streak values
      });

      // Load course videos after courses are loaded
      if (_courses.isNotEmpty) {
        await _loadAllCourseVideos();
      }

      _performanceMonitor.endOperation('homepage_data_load');
    } catch (e) {
      if (mounted) {
        final errorStr = e.toString().toLowerCase();
        if (errorStr.contains('401') ||
            errorStr.contains('session expired') ||
            errorStr.contains('not authenticated') ||
            errorStr.contains('device removed') ||
            errorStr.contains('device_mismatch')) {
          Navigator.pushNamedAndRemoveUntil(context, '/login', (route) => false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Session expired or device removed. Please log in again.'), backgroundColor: Colors.red),
          );
        } else if (errorStr.contains('socketexception') ||
                   errorStr.contains('failed host lookup') ||
                   errorStr.contains('network error') ||
                   errorStr.contains('timeout')) {
          setState(() {
            _errorMessage = 'No internet connection. Please check your network and try again.';
            _isLoadingProfile = false;
            _isLoadingCourses = false;
            _isLoadingQuote = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('No internet connection.'), backgroundColor: Colors.orange),
          );
        } else {
          setState(() {
            _errorMessage = 'An unexpected error occurred: $e';
            _isLoadingProfile = false;
            _isLoadingCourses = false;
            _isLoadingQuote = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('An error occurred: $e'), backgroundColor: Colors.red),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isWide = MediaQuery.of(context).size.width > 700;
    final screenWidth = MediaQuery.of(context).size.width;

    // Debug prints
    print('DEBUG: _profile = \\${_profile}');
    print('DEBUG: assignedStaffId = \\${_profile?.assignedStaffId}');

    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      body: Stack(
        children: [
          RefreshIndicator(
            onRefresh: () => _loadAllData(),
            child: CustomScrollView(
              controller: _scrollController,
              physics: const AlwaysScrollableScrollPhysics(parent: BouncingScrollPhysics()),
              slivers: [
                // Header
                SliverAppBar(
                  expandedHeight: 140.0,
                  collapsedHeight: 72.0,
                  pinned: true,
                  elevation: 0,
              backgroundColor: theme.brightness == Brightness.dark
                  ? KFTDesignSystem.darkSurfaceColor
                  : theme.colorScheme.primary,
              systemOverlayStyle: SystemUiOverlayStyle(
                statusBarColor: Colors.transparent,
                statusBarIconBrightness: Brightness.light,
                statusBarBrightness: Brightness.dark,
              ),
              actions: [
                // Theme Toggle Button
                const ThemeToggleButton(iconOnly: true),

                // Streak Icon Button with improved spacing
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: Builder(
                    builder: (context) => IconButton(
                      icon: Stack(
                        alignment: Alignment.center,
                        children: [
                          // Clean, modern fire icon
                          Icon(
                            Icons.local_fire_department,
                            color: Colors.deepOrangeAccent,
                            size: 26,
                          ),
                          // Streak count badge
                          Positioned(
                            bottom: 2,
                            right: 2,
                            child: Container(
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.08),
                                    blurRadius: 4,
                                  ),
                                ],
                              ),
                              child: Text(
                                '$_currentStreak',
                                style: TextStyle(
                                  color: Colors.deepOrangeAccent,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 10,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      tooltip: 'Daily Streak',
                      onPressed: () {
                        showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          backgroundColor: Colors.transparent,
                          builder: (context) {
                            final theme = Theme.of(context);
                            final isDark = theme.brightness == Brightness.dark;
                            return Container(
                              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
                              decoration: BoxDecoration(
                                color: isDark ? Colors.grey[900] : Colors.white,
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(20),
                                  topRight: Radius.circular(20),
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.08),
                                    blurRadius: 16,
                                    offset: const Offset(0, -4),
                                  ),
                                ],
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Container(
                                    width: 40,
                                    height: 4,
                                    margin: const EdgeInsets.only(bottom: 16),
                                    decoration: BoxDecoration(
                                      color: Colors.grey[400],
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),
                                  Icon(Icons.local_fire_department, color: theme.colorScheme.secondary, size: 36),
                                  const SizedBox(height: 8),
                                  Text('Your Daily Streak', style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
                                  const SizedBox(height: 8),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      _buildStreakStat(theme, 'Current', _currentStreak, theme.colorScheme.primary),
                                      const SizedBox(width: 24),
                                      _buildStreakStat(theme, 'Best', _highestStreak, Colors.amber),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  Divider(),
                                  const SizedBox(height: 8),
                                  Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text('Streak History', style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600)),
                                  ),
                                  const SizedBox(height: 8),
                                  FutureBuilder<List<DateTime>>(
                                    future: _getStreakHistory(),
                                    builder: (context, snapshot) {
                                      if (!snapshot.hasData) {
                                        return const Center(child: CircularProgressIndicator());
                                      }
                                      final history = snapshot.data!;
                                      if (history.isEmpty) {
                                        return Text('No streak history yet.', style: theme.textTheme.bodyMedium);
                                      }
                                      return SizedBox(
                                        height: 180,
                                        child: ListView.builder(
                                          itemCount: history.length,
                                          itemBuilder: (context, i) {
                                            final date = history[i];
                                            final isMilestone = [7, 30, 100].contains(i + 1);
                                            return ListTile(
                                              leading: Icon(
                                                isMilestone ? Icons.emoji_events : Icons.check_circle,
                                                color: isMilestone ? Colors.amber : theme.colorScheme.primary,
                                              ),
                                              title: Text(DateFormat('EEE, MMM d, yyyy').format(date)),
                                              subtitle: isMilestone
                                                  ? Text('Milestone: ${(i + 1)} days!', style: TextStyle(color: Colors.amber, fontWeight: FontWeight.bold))
                                                  : null,
                                            );
                                          },
                                        ),
                                      );
                                    },
                                  ),
                                  const SizedBox(height: 16),
                                  if (_isMilestone)
                                    Container(
                                      padding: const EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: Colors.greenAccent.withOpacity(0.15),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        "Congrats! You've reached a $_currentStreak-day streak! Keep it up!",
                                        style: theme.textTheme.bodyLarge?.copyWith(color: Colors.green.shade900, fontWeight: FontWeight.bold),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                ],
                              ),
                            );
                          },
                        );
                      },
                    ),
                  ),
                ),

                // Simple Notification Icon
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: SimpleNotificationIcon(
                    iconColor: theme.brightness == Brightness.dark
                        ? KFTDesignSystem.darkTextPrimaryColor
                        : Colors.white,
                    iconSize: 24,
                    showBadge: false, // Can be enhanced later with actual notification count
                    onPressed: () {
                      Navigator.pushNamed(context, '/notifications');
                    },
                  ),
                ),

                // Enhanced Profile Avatar
                Padding(
                  padding: const EdgeInsets.only(right: 16, left: 8),
                  child: ProfileAvatarEnhanced(
                    userProfile: _profile,
                    isLoading: _isLoadingProfile,
                    radius: 18,
                    showDropdown: true,
                    backgroundColor: theme.brightness == Brightness.dark
                        ? KFTDesignSystem.darkTextPrimaryColor.withOpacity(0.15)
                        : Colors.white.withOpacity(0.15),
                    foregroundColor: theme.brightness == Brightness.dark
                        ? KFTDesignSystem.darkTextPrimaryColor
                        : Colors.white,
                  ),
                ),
              ],
              flexibleSpace: LayoutBuilder(
                builder: (BuildContext context, BoxConstraints constraints) {
                  final expandedHeight = 140.0;
                  final scrollPercentage = (1.0 - (constraints.maxHeight / expandedHeight)).clamp(0.0, 1.0);
                  final isDarkMode = theme.brightness == Brightness.dark;
                  final textColor = isDarkMode
                      ? KFTDesignSystem.darkTextPrimaryColor
                      : Colors.white;
                  final decorElementColor = isDarkMode
                      ? Colors.white.withOpacity(0.07)
                      : Colors.white.withOpacity(0.1);
                  final decorElementColor2 = isDarkMode
                      ? Colors.white.withOpacity(0.05)
                      : Colors.white.withOpacity(0.08);
                  return FlexibleSpaceBar(
                    titlePadding: EdgeInsets.zero,
                    title: AnimatedOpacity(
                      duration: const Duration(milliseconds: 300),
                      opacity: scrollPercentage > 0.5 ? 1.0 : 0.0,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 20, bottom: 8),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              _greeting + '!',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w300,
                                color: textColor.withOpacity(0.85),
                                fontFamily: KFTDesignSystem.fontFamily,
                                letterSpacing: 0.3,
                              ),
                            ),
                            Text(
                              _isLoadingProfile ? 'Loading...' : (_profile?.name ?? 'User'),
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w500,
                                color: textColor,
                                fontFamily: KFTDesignSystem.fontFamily,
                                letterSpacing: -0.3,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ),
                    background: Stack(
                      children: [
                        // Gradient background
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: isDarkMode
                                  ? [
                                      KFTDesignSystem.darkSurfaceColor,
                                      KFTDesignSystem.darkSurfaceColor.withOpacity(0.9),
                                    ]
                                  : [
                                      theme.colorScheme.primary,
                                      theme.colorScheme.primary.withOpacity(0.9),
                                    ],
                            ),
                          ),
                        ),
                        // Decorative elements
                        Positioned(
                          top: -20,
                          right: -20,
                          child: Container(
                            width: 150,
                            height: 150,
                            decoration: BoxDecoration(
                              color: decorElementColor,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                        Positioned(
                          bottom: -50,
                          left: -30,
                          child: Container(
                            width: 180,
                            height: 180,
                            decoration: BoxDecoration(
                              color: decorElementColor2,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                        // Content
                        SafeArea(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      _greeting + '!',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w300,
                                        color: textColor.withOpacity(0.85),
                                        fontFamily: KFTDesignSystem.fontFamily,
                                        letterSpacing: 0.5,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      _isLoadingProfile ? 'Loading...' : (_profile?.name ?? 'User'),
                                      style: TextStyle(
                                        fontSize: 28,
                                        fontWeight: FontWeight.w200,
                                        color: textColor,
                                        fontFamily: KFTDesignSystem.fontFamily,
                                        letterSpacing: -0.5,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 20),
                              ],
                            ),
                          ),
                        ),
                        // Blurred overlay
                        Positioned.fill(
                          child: IgnorePointer(
                            child: AnimatedOpacity(
                              opacity: scrollPercentage,
                              duration: const Duration(milliseconds: 200),
                              child: BackdropFilter(
                                filter: ImageFilter.blur(
                                  sigmaX: 10 * scrollPercentage,
                                  sigmaY: 10 * scrollPercentage,
                                ),
                                child: Container(
                                  color: isDarkMode
                                      ? KFTDesignSystem.darkSurfaceColor.withOpacity(0.7 * scrollPercentage)
                                      : theme.colorScheme.primary.withOpacity(0.5 * scrollPercentage),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
                ),
                // Main Content
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: isWide ? screenWidth * 0.15 : 20,
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 20),

                          _buildNutritionStories(theme),
                          const SizedBox(height: 20),

                          // Hydration Tracker Widget - Only show if configured
                          Column(
                            children: [
                              const HydrationHomeWidget(),
                              const SizedBox(height: 20),
                            ],
                          ),

                          // Motivational Quote Section
                          if (_deepSeekEnabled)
                            Builder(
                              builder: (context) {
                                if (_isLoadingQuote) {
                                  return DailyMotivationalQuote(
                                quote: MotivationalQuote(
                                  quote: '',
                                  author: '',
                                  category: '',
                                ),
                                isLoading: true,
                                showActions: false,
                              );
                            } else if (_quote != null) {
                              return SlideTransition(
                                position: Tween<Offset>(
                                  begin: const Offset(0, 0.2),
                                  end: Offset.zero,
                                ).animate(CurvedAnimation(
                                  parent: _animationController,
                                  curve: const Interval(0.2, 0.6, curve: Curves.easeOut),
                                )),
                                child: FadeTransition(
                                  opacity: Tween<double>(begin: 0, end: 1).animate(
                                    CurvedAnimation(
                                      parent: _animationController,
                                      curve: const Interval(0.2, 0.6, curve: Curves.easeOut),
                                    ),
                                  ),
                                  child: DailyMotivationalQuote(
                                    quote: _quote!,
                                    isLoading: false,
                                    showActions: false,
                                  ),
                                ),
                              );
                            } else {
                              return Container(
                                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).colorScheme.surface,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: Theme.of(context).dividerColor.withOpacity(0.1)),
                                ),
                                child: Row(
                                  children: [
                                    Icon(Icons.error_outline, color: Theme.of(context).colorScheme.error),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        'Could not load a motivational quote. Please try again later.',
                                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                                }
                              },
                            ),
                          const SizedBox(height: 24),
                          // Recent Videos Section - Minimal Design
                          Padding(
                            padding: const EdgeInsets.only(bottom: 12),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // More minimal section title
                                Text(
                                  'Continue Watching',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: theme.colorScheme.onBackground.withOpacity(0.9),
                                    letterSpacing: -0.3,
                                  ),
                                ),
                                // Minimal "See All" button without icon
                                GestureDetector(
                              onTap: () {
                                if (_courses.isNotEmpty) {
                                  // Get the default course ID (first course)
                                  final defaultCourseId = _courses.isNotEmpty ? _courses.first.id : 0;

                                  // Navigate to the CourseVideosPage
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => CourseVideosPage(courseId: defaultCourseId),
                                    ),
                                  ).then((_) {
                                    // Refresh data when returning from CourseVideosPage
                                    _loadAllData();
                                  });
                                }
                              },
                              child: Text(
                                'See all',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: theme.colorScheme.primary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                          // Recent Videos - Minimal Horizontal scrollable
                          if (_isLoadingCourses)
                            SizedBox(
                              height: 140,
                              child: ListView.separated(
                                scrollDirection: Axis.horizontal,
                                itemCount: 3, // Show 3 skeleton videos
                                separatorBuilder: (context, i) => const SizedBox(width: 12),
                                itemBuilder: (context, index) {
                              return Container(
                                width: 180,
                                child: SkeletonWidget(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      SkeletonContainer(
                                        width: 180,
                                        height: 100,
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      const SizedBox(height: 8),
                                      SkeletonContainer(
                                        width: 140,
                                        height: 16,
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      const SizedBox(height: 4),
                                      SkeletonContainer(
                                        width: 80,
                                        height: 12,
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                            )
                          else if (_courses.isEmpty)
                            Center(
                              child: Text(
                                'No videos available',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: theme.colorScheme.onBackground.withOpacity(0.6),
                                ),
                              ),
                            )
                          else
                            SizedBox(
                              height: 140, // Reduced height for more minimal design
                              child: _buildRecentVideosSection(theme),
                            ),

                          const SizedBox(height: 32),

                          // Courses Section
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Your Courses',
                                style: theme.textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.onBackground,
                                  letterSpacing: -0.5,
                                ),
                              ),
                              // Only show sort button when multiple courses are available
                              if (_courses.length > 1)
                                Material(
                              color: Colors.transparent,
                              borderRadius: BorderRadius.circular(12),
                              child: InkWell(
                                borderRadius: BorderRadius.circular(12),
                                onTap: () {
                                  // TODO: Implement sorting functionality
                                },
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: theme.colorScheme.primary.withOpacity(0.08),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: theme.colorScheme.primary.withOpacity(0.2),
                                      width: 1,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.sort_rounded,
                                        size: 18,
                                        color: theme.colorScheme.primary,
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        'Sort',
                                        style: TextStyle(
                                          color: theme.colorScheme.primary,
                                          fontWeight: FontWeight.w500,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                                ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          // Horizontal scrollable courses
                          if (_isLoadingCourses)
                            SizedBox(
                              height: 300,
                              child: ListView.separated(
                                scrollDirection: Axis.horizontal,
                                itemCount: 3, // Show 3 skeleton cards
                                separatorBuilder: (context, i) => const SizedBox(width: 16),
                                itemBuilder: (context, index) {
                                  return SizedBox(
                                    width: 300,
                                    child: SkeletonCourseCard(),
                                  );
                                },
                              ),
                            )
                          else if (_errorMessage.isNotEmpty)
                            Center(
                              child: Text(
                                _errorMessage,
                                style: theme.textTheme.bodyLarge?.copyWith(
                                  color: AppTheme.error,
                                ),
                              ),
                            )
                          else if (_courses.isEmpty)
                            Center(
                              child: Text(
                                'No courses found',
                                style: theme.textTheme.bodyLarge,
                              ),
                            )
                          else
                            SizedBox(
                              height: 300, // Increased height to prevent overflow
                              child: Stack(
                                children: [
                                  // Horizontal scrollable courses
                                  ListView.separated(
                                    scrollDirection: Axis.horizontal,
                                    itemCount: _courses.length,
                                    separatorBuilder: (context, i) => const SizedBox(width: 16),
                                    itemBuilder: (context, index) {
                                      final course = _courses[index];
                                      return SizedBox(
                                        width: 300,
                                        child: _buildCourseCard(theme, course),
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
                // Bottom padding
                const SliverToBoxAdapter(
                  child: SizedBox(height: 100),
                ),
              ],
            ),
          ),

          // Staff Name Animation Overlay - Disabled
          // if (_showStaffAnimation && _staffDataLoaded)
          //   StaffNameAnimationWidget(
          //     profile: _profile,
          //     shouldAnimate: false, // Always disabled
          //     onAnimationComplete: _onStaffAnimationComplete,
          //   ),
        ],
      ),
      floatingActionButton: EnhancedWhatsAppSupportFAB(
        profile: _profile,
        onStaffDataLoaded: _onStaffDataLoaded,
      ),
    );
  }

  Widget _buildActiveCourseBanner(ThemeData theme, Course latestCourse) {
    final isDarkMode = theme.brightness == Brightness.dark;
    final containerColor = isDarkMode
        ? Colors.white.withOpacity(0.12)
        : Colors.white.withOpacity(0.18);
    final iconContainerColor = isDarkMode
        ? Colors.white.withOpacity(0.18)
        : Colors.white.withOpacity(0.25);
    final textColor = Colors.white;
    final subtitleColor = Colors.white.withOpacity(0.85);

    return Material(
      color: Colors.transparent,
      borderRadius: BorderRadius.circular(14),
      child: InkWell(
        borderRadius: BorderRadius.circular(14),
        onTap: () {
          // Play the last played video directly without any other logic
          _playLastVideo(latestCourse);
        },
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: containerColor,
            borderRadius: BorderRadius.circular(14),
            border: Border.all(
              color: Colors.white.withOpacity(0.1),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: iconContainerColor,
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Icon(
                  Icons.play_circle_fill_rounded,
                  color: textColor,
                  size: 26,
                ),
              ),
              const SizedBox(width: 14),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Continue Learning',
                      style: TextStyle(
                        color: subtitleColor,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                        letterSpacing: 0.2,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      latestCourse.title,
                      style: TextStyle(
                        color: textColor,
                        fontSize: 17,
                        fontWeight: FontWeight.w600,
                        letterSpacing: -0.2,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.amber.withOpacity(0.9),
                  borderRadius: BorderRadius.circular(14),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.amber.withOpacity(0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Text(
                  '${latestCourse.progressPercentage}%',
                  style: TextStyle(
                    color: Colors.black87,
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.2,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCourseCard(ThemeData theme, Course course) {
    final isDarkMode = theme.brightness == Brightness.dark;
    final status = course.progressPercentage >= 100
        ? 'Completed'
        : (course.progressPercentage > 0 ? 'Active' : 'New');
    final statusColor = status == 'Completed'
        ? Colors.green
        : (status == 'Active' ? Colors.blue : Colors.orange);
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 1.0, end: 1.0),
      duration: const Duration(milliseconds: 200),
      builder: (context, scale, child) {
        return GestureDetector(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => CourseVideosPage(courseId: course.id),
              ),
            );
          },
          onLongPress: () {
            // Show a quick preview of available videos
            _showQuickVideoPreview(course);
          },
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeOut,
            transform: Matrix4.identity()..scale(scale),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface.withOpacity(isDarkMode ? 0.7 : 0.85),
              borderRadius: BorderRadius.circular(24),
              border: Border.all(
                color: theme.dividerColor.withOpacity(0.12),
                width: 1.2,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.07),
                  blurRadius: 18,
                  offset: const Offset(0, 8),
                ),
              ],
              // Glassmorphism effect
              backgroundBlendMode: BlendMode.overlay,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min, // Prevent overflow
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Course header with image or fallback
                Stack(
                  children: [
                    ClipRRect(
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
                      child: AspectRatio(
                        aspectRatio: 16 / 9,
                        child: course.thumbnailUrl?.isNotEmpty == true
                            ? OptimizedImageWidget(
                                imageUrl: getFullImageUrl(course.thumbnailUrl),
                                fit: BoxFit.cover,
                                errorWidget: Container(
                                  color: theme.colorScheme.primary.withOpacity(0.1),
                                  child: Center(
                                    child: Icon(
                                      Icons.fitness_center,
                                      size: 48,
                                      color: theme.colorScheme.primary,
                                    ),
                                  ),
                                ),
                              )
                            : Container(
                                color: theme.colorScheme.primary.withOpacity(0.1),
                                child: Center(
                                  child: Icon(
                                    Icons.fitness_center,
                                    size: 48,
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                              ),
                      ),
                    ),
                    // Overlay gradient for readability
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              theme.colorScheme.surface.withOpacity(0.18),
                            ],
                          ),
                        ),
                      ),
                    ),
                    // Status badge
                    Positioned(
                      top: 10,
                      left: 10,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                        decoration: BoxDecoration(
                          color: statusColor.withOpacity(0.9),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          status,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 11,
                            letterSpacing: 0.2,
                          ),
                        ),
                      ),
                    ),

                    // Quick play button - Instant access to videos
                    Positioned(
                      bottom: 12,
                      right: 12,
                      child: GestureDetector(
                        onTap: () {
                          // Play the next video in the course
                          _playNextCourseVideo(course);
                        },
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.play_arrow_rounded,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                // Course details
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
                    child: Column(
                      mainAxisSize: MainAxisSize.min, // Prevent overflow
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          course.title,
                          style: const TextStyle(
                            fontSize: 16, // Slightly smaller font
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4), // Reduced spacing
                        Text(
                          course.description,
                          style: TextStyle(
                            fontSize: 12, // Smaller font
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                            height: 1.2, // Reduced line height
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const Spacer(),
                        // Progress bar
                        ClipRRect(
                          borderRadius: BorderRadius.circular(6),
                          child: LinearProgressIndicator(
                            value: course.progressPercentage / 100,
                            minHeight: 5,
                            backgroundColor: theme.colorScheme.primary.withOpacity(0.08),
                            valueColor: AlwaysStoppedAnimation<Color>(
                              course.progressPercentage >= 100
                                  ? Colors.green
                                  : theme.colorScheme.primary,
                            ),
                          ),
                        ),
                        const SizedBox(height: 12),

                        // Bottom row - Course stats and view all button
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // Progress and video count
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${course.progressPercentage}% Complete',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                    color: isDarkMode ? Colors.white70 : Colors.black87,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  '${course.completedVideos}/${course.totalVideos} videos',
                                  style: TextStyle(
                                    fontSize: 11,
                                    color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),

                            // View All button
                            GestureDetector(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => CourseVideosPage(courseId: course.id),
                                  ),
                                );
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: theme.colorScheme.primary.withOpacity(0.12),
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Text(
                                  'View All',
                                  style: TextStyle(
                                    color: theme.colorScheme.primary,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatCard(ThemeData theme, String label, String value, IconData icon, {Color? color}) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color ?? theme.colorScheme.primary, size: 28),
            const SizedBox(height: 6),
            Text(value, style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
            Text(label, style: theme.textTheme.bodySmall?.copyWith(color: theme.hintColor)),
          ],
        ),
      ),
    );
  }

  Widget _buildBadge(ThemeData theme, String label, IconData icon, {bool achieved = false}) {
    return Padding(
      padding: const EdgeInsets.only(right: 12),
      child: Column(
        children: [
          CircleAvatar(
            backgroundColor: achieved ? theme.colorScheme.primary : theme.dividerColor,
            child: Icon(
              icon,
              color: achieved
                  ? Colors.white
                  : theme.brightness == Brightness.dark
                      ? Colors.white54
                      : Colors.grey[400],
            ),
          ),
          const SizedBox(height: 4),
          Text(label, style: theme.textTheme.bodySmall?.copyWith(fontSize: 11)),
        ],
      ),
    );
  }

  Widget _buildProgressAction(ThemeData theme, String label, IconData icon, VoidCallback onTap) {
    return Column(
      children: [
        InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: CircleAvatar(
            backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
            child: Icon(icon, color: theme.colorScheme.primary),
          ),
        ),
        const SizedBox(height: 4),
        Text(label, style: theme.textTheme.bodySmall),
      ],
    );
  }

  String getFullImageUrl(String? url) {
    if (url == null || url.isEmpty) return '';
    if (url.startsWith('http://') || url.startsWith('https://')) return url;
    final cleanPath = url.startsWith('/') ? url.substring(1) : url;
    return '${ApiService.baseUrl.replaceAll('/api', '')}/$cleanPath';
  }

  // Play the last played video directly without any other logic
  void _playLastVideo(Course course) async {
    try {
      // Get the globally last accessed video ID from CourseTrackingService
      final trackingService = CourseTrackingService();
      final lastAccessedVideoId = await trackingService.getLastAccessedVideoId();

      if (lastAccessedVideoId != null) {
        // Search for the video across all loaded course videos
        CourseVideo? lastAccessedVideo;

        for (final courseVideos in _courseVideos.values) {
          for (final video in courseVideos) {
            if (video.id == lastAccessedVideoId) {
              lastAccessedVideo = video;
              break;
            }
          }
          if (lastAccessedVideo != null) break;
        }

        if (lastAccessedVideo != null) {
          // Play the last accessed video regardless of which course it's from
          _openDedicatedVideoPlayer(lastAccessedVideo);
          return;
        }
      }

      // Fallback: If no last accessed video, get videos from the current course
      final videos = _courseVideos[course.id] ?? [];

      // If there are no videos, play a mock video
      if (videos.isEmpty) {
        _playNextCourseVideo(course);
        return;
      }

      // Find the first available video as fallback
      final fallbackVideo = videos.first;

      // Open dedicated video player instead of overlay
      _openDedicatedVideoPlayer(fallbackVideo);
    } catch (e) {
      debugPrint('Error getting last accessed video: $e');
      // Fallback to course-specific logic
      final videos = _courseVideos[course.id] ?? [];
      if (videos.isNotEmpty) {
        _openDedicatedVideoPlayer(videos.first);
      } else {
        _playNextCourseVideo(course);
      }
    }
  }

  // Play the next video in a course
  void _playNextCourseVideo(Course course) {
    // Get the course videos for this course
    final videos = _courseVideos[course.id] ?? [];

    // If there are videos available, play the first unlocked one
    if (videos.isNotEmpty) {
      final nextVideo = videos.firstWhere(
        (video) => video.isUnlocked && !video.isCompleted,
        orElse: () => videos.first, // Fallback to first video
      );

      // Open dedicated video player
      _openDedicatedVideoPlayer(nextVideo);
      return;
    }

    // If no videos are available, create a mock video for demonstration
    final mockVideo = CourseVideo(
      id: 1,
      title: 'Next Video in ${course.title}',
      description: 'Continue your progress in ${course.title}',
      videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
      weekNumber: 1,
      sequenceNumber: 1,
      isUnlocked: true,
      isCompleted: false,
      videoProvider: 'other',
    );

    // Open dedicated video player instead of overlay
    _openDedicatedVideoPlayer(mockVideo);
  }

  // Show a quick preview of available videos in a course
  void _showQuickVideoPreview(Course course) {
    // Mock data for videos in this course
    final List<Map<String, dynamic>> courseVideos = [
      {
        'id': 1,
        'title': 'Warmup & Mobility',
        'duration': '15 min',
        'unlocked': true,
        'completed': course.progressPercentage > 25,
      },
      {
        'id': 2,
        'title': 'HIIT Session 1',
        'duration': '30 min',
        'unlocked': true,
        'completed': course.progressPercentage > 50,
      },
      {
        'id': 3,
        'title': 'HIIT Session 2',
        'duration': '30 min',
        'unlocked': course.progressPercentage > 25,
        'completed': course.progressPercentage > 75,
      },
      {
        'id': 4,
        'title': 'Cooldown & Stretch',
        'duration': '10 min',
        'unlocked': course.progressPercentage > 50,
        'completed': course.progressPercentage >= 100,
      },
    ];

    // Show bottom sheet with videos
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        final theme = Theme.of(context);
        final isDarkMode = theme.brightness == Brightness.dark;

        return Container(
          height: MediaQuery.of(context).size.height * 0.7,
          decoration: BoxDecoration(
            color: isDarkMode ? Colors.grey[900] : Colors.white,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle
              Container(
                margin: const EdgeInsets.only(top: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[400],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    if (course.thumbnailUrl != null) ...[
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          getFullImageUrl(course.thumbnailUrl),
                          width: 60,
                          height: 60,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Container(
                            width: 60,
                            height: 60,
                            color: theme.colorScheme.primary.withOpacity(0.1),
                            child: Icon(
                              Icons.fitness_center,
                              color: theme.colorScheme.primary,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                    ],

                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            course.title,
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: isDarkMode ? Colors.white : Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${course.progressPercentage}% Complete',
                            style: TextStyle(
                              fontSize: 14,
                              color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                            ),
                          ),
                        ],
                      ),
                    ),

                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),

              // Progress bar with hydrated theme
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: HydratedProgressBar(
                  progress: course.progressPercentage / 100,
                  height: 6,
                  animated: true,
                  showShimmer: true,
                  progressGradient: course.progressPercentage >= 100
                      ? const LinearGradient(
                          colors: [Color(0xFF10B981), Color(0xFF14B8A6)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        )
                      : const LinearGradient(
                          colors: [Color(0xFF06B6D4), Color(0xFF0891B2)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                ),
              ),

              const SizedBox(height: 16),

              // Videos list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: courseVideos.length,
                  itemBuilder: (context, index) {
                    final video = courseVideos[index];
                    final isUnlocked = video['unlocked'] as bool;
                    final isCompleted = video['completed'] as bool;

                    return ListTile(
                      contentPadding: EdgeInsets.zero,
                      leading: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: isUnlocked
                              ? (isCompleted
                                  ? Colors.green.withOpacity(0.1)
                                  : theme.colorScheme.primary.withOpacity(0.1))
                              : Colors.grey.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          isUnlocked
                              ? (isCompleted ? Icons.check : Icons.play_arrow)
                              : Icons.lock,
                          color: isUnlocked
                              ? (isCompleted ? Colors.green : theme.colorScheme.primary)
                              : Colors.grey,
                        ),
                      ),
                      title: Text(
                        video['title'] as String,
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: isUnlocked
                              ? (isDarkMode ? Colors.white : Colors.black87)
                              : Colors.grey,
                        ),
                      ),
                      subtitle: Text(
                        video['duration'] as String,
                        style: TextStyle(
                          fontSize: 12,
                          color: isUnlocked
                              ? (isDarkMode ? Colors.grey[400] : Colors.grey[700])
                              : Colors.grey,
                        ),
                      ),
                      trailing: isUnlocked
                          ? IconButton(
                              icon: const Icon(Icons.play_circle_outline),
                              color: theme.colorScheme.primary,
                              onPressed: () {
                                Navigator.pop(context);

                                // Create a CourseVideo object
                                final courseVideo = CourseVideo(
                                  id: video['id'] as int,
                                  title: video['title'] as String,
                                  description: 'Video from ${course.title}',
                                  videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                                  weekNumber: 1,
                                  sequenceNumber: index + 1,
                                  isUnlocked: true,
                                  isCompleted: isCompleted,
                                  videoProvider: 'other',
                                );

                                // Open dedicated video player
                                _openDedicatedVideoPlayer(courseVideo);
                              },
                            )
                          : null,
                      onTap: isUnlocked
                          ? () {
                              Navigator.pop(context);

                              // Create a CourseVideo object
                              final courseVideo = CourseVideo(
                                id: video['id'] as int,
                                title: video['title'] as String,
                                description: 'Video from ${course.title}',
                                videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                                weekNumber: 1,
                                sequenceNumber: index + 1,
                                isUnlocked: true,
                                isCompleted: isCompleted,
                                videoProvider: 'other',
                              );

                              // Open dedicated video player
                              _openDedicatedVideoPlayer(courseVideo);
                            }
                          : null,
                    );
                  },
                ),
              ),

              // View all button
              Padding(
                padding: const EdgeInsets.all(16),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    onPressed: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => CourseVideosPage(courseId: course.id),
                        ),
                      );
                    },
                    child: const Text('View All Videos'),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Build the recent videos section with minimal design using real course videos
  Widget _buildRecentVideosSection(ThemeData theme) {
    final isDarkMode = theme.brightness == Brightness.dark;

    // Get all videos from loaded courses
    List<CourseVideo> allVideos = [];
    Map<int, Course> courseMap = {};

    // Create a map of course ID to Course object for quick lookup
    for (var course in _courses) {
      courseMap[course.id] = course;

      // Add videos from this course if available
      if (_courseVideos.containsKey(course.id)) {
        allVideos.addAll(_courseVideos[course.id]!);
      }
    }

    // Find the current week (earliest week with unlocked but not all completed videos)
    int? currentWeek;
    Map<int, List<CourseVideo>> videosByWeek = {};

    // Group videos by week
    for (final video in allVideos) {
      if (!videosByWeek.containsKey(video.weekNumber)) {
        videosByWeek[video.weekNumber] = [];
      }
      videosByWeek[video.weekNumber]!.add(video);
    }

    // Find current week
    for (final weekNumber in videosByWeek.keys.toList()..sort()) {
      final weekVideos = videosByWeek[weekNumber]!;
      final allCompleted = weekVideos.every((v) => v.isCompleted);
      final anyUnlocked = weekVideos.any((v) => v.isUnlocked);

      if (anyUnlocked && !allCompleted) {
        currentWeek = weekNumber;
        break;
      }
    }

    // If no current week found, use the earliest week with any unlocked videos
    if (currentWeek == null) {
      for (final weekNumber in videosByWeek.keys.toList()..sort()) {
        final weekVideos = videosByWeek[weekNumber]!;
        final anyUnlocked = weekVideos.any((v) => v.isUnlocked);

        if (anyUnlocked) {
          currentWeek = weekNumber;
          break;
        }
      }
    }

    // Filter videos to show from current week onward, only unlocked videos
    List<CourseVideo> continueWatchingVideos = [];
    if (currentWeek != null) {
      for (final weekNumber in videosByWeek.keys.toList()..sort()) {
        if (weekNumber >= currentWeek) {
          final weekVideos = videosByWeek[weekNumber]!
              .where((v) => v.isUnlocked)
              .toList();

          // Sort videos within the week by sequence number
          weekVideos.sort((a, b) => a.sequenceNumber.compareTo(b.sequenceNumber));
          continueWatchingVideos.addAll(weekVideos);
        }
      }
    } else {
      // Fallback: if no current week found, show all unlocked videos ordered by week and sequence
      continueWatchingVideos = allVideos.where((v) => v.isUnlocked).toList();
      continueWatchingVideos.sort((a, b) {
        int weekComparison = a.weekNumber.compareTo(b.weekNumber);
        if (weekComparison != 0) return weekComparison;
        return a.sequenceNumber.compareTo(b.sequenceNumber);
      });
    }

    // Take the first 5 videos
    final recentVideos = continueWatchingVideos.take(5).toList();

    // If no videos are loaded yet, show loading indicator
    if (_isLoadingCourseVideos) {
      return const Center(
        child: SizedBox(
          height: 100,
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    // If no videos are available, show a message
    if (recentVideos.isEmpty) {
      return Center(
        child: Text(
          'No videos available',
          style: TextStyle(
            fontSize: 14,
            color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
          ),
        ),
      );
    }

    return ListView.separated(
      scrollDirection: Axis.horizontal,
      itemCount: recentVideos.length,
      padding: const EdgeInsets.symmetric(horizontal: 2), // Minimal padding
      separatorBuilder: (context, i) => const SizedBox(width: 12), // Reduced spacing
      itemBuilder: (context, index) {
        final video = recentVideos[index];

        // Find the course this video belongs to
        final course = courseMap[video.id] ?? _courses.first;

        // Calculate progress value (0.0 to 1.0)
        double progressValue = 0.0;
        if (video.lastPositionSeconds != null && video.durationMinutes != null) {
          final totalSeconds = video.durationMinutes! * 60;
          if (totalSeconds > 0) {
            progressValue = video.lastPositionSeconds! / totalSeconds;
            // Cap at 0.99 to show it's not complete unless marked as completed
            if (progressValue > 0.99 && !video.isCompleted) {
              progressValue = 0.99;
            }
          }
        }

        // Format duration string
        String durationStr = video.durationMinutes != null
            ? '${video.durationMinutes} min'
            : '';

        return GestureDetector(
          onTap: () {
            // Open dedicated video player
            _openDedicatedVideoPlayer(video);
          },
          // Minimal video card design
          child: Container(
            width: 180, // Narrower width for minimal design
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8), // Smaller radius
              color: Colors.transparent, // Transparent background
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Thumbnail with minimal styling
                Stack(
                  children: [
                    // Thumbnail with rounded corners
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: AspectRatio(
                        aspectRatio: 16 / 9,
                        child: video.thumbnailUrl != null
                            ? Image.network(
                                getFullImageUrl(video.thumbnailUrl!),
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  // Fallback to course thumbnail if video thumbnail fails
                                  final courseThumbnail = course.thumbnailUrl;
                                  if (courseThumbnail != null && courseThumbnail.isNotEmpty) {
                                    return Image.network(
                                      getFullImageUrl(courseThumbnail),
                                      fit: BoxFit.cover,
                                      errorBuilder: (context, error, stackTrace) {
                                        return _buildPlaceholderThumbnail(isDarkMode);
                                      },
                                    );
                                  }
                                  return _buildPlaceholderThumbnail(isDarkMode);
                                },
                              )
                            : course.thumbnailUrl != null && course.thumbnailUrl!.isNotEmpty
                                ? Image.network(
                                    getFullImageUrl(course.thumbnailUrl!),
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return _buildPlaceholderThumbnail(isDarkMode);
                                    },
                                  )
                                : _buildPlaceholderThumbnail(isDarkMode),
                      ),
                    ),

                    // Minimal play button - just an icon
                    Positioned.fill(
                      child: Center(
                        child: Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.4),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.play_arrow_rounded,
                            color: Colors.white,
                            size: 24, // Smaller icon
                          ),
                        ),
                      ),
                    ),

                    // Minimal duration badge
                    if (durationStr.isNotEmpty)
                      Positioned(
                        bottom: 8,
                        right: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.5),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            durationStr,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),

                    // Progress indicator - more subtle
                    if (progressValue > 0)
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: LinearProgressIndicator(
                          value: progressValue,
                          backgroundColor: Colors.transparent,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            theme.colorScheme.primary.withOpacity(0.8)
                          ),
                          minHeight: 2, // Thinner progress bar
                        ),
                      ),
                  ],
                ),

                // Minimal video info
                Padding(
                  padding: const EdgeInsets.only(top: 6, left: 2),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Video title - minimal styling
                      Text(
                        video.title,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: isDarkMode ? Colors.white : Colors.black87,
                          letterSpacing: -0.2,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      // Course name - very subtle
                      Text(
                        course.title,
                        style: TextStyle(
                          fontSize: 10,
                          color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Helper method to build a placeholder thumbnail
  Widget _buildPlaceholderThumbnail(bool isDarkMode) {
    return Container(
      color: isDarkMode ? Colors.grey[800] : Colors.grey[200],
      child: Center(
        child: Icon(
          Icons.fitness_center,
          color: isDarkMode ? Colors.grey[600] : Colors.grey[400],
          size: 24,
        ),
      ),
    );
  }

  // Build WhatsApp/Instagram style nutrition stories
  Widget _buildNutritionStories(ThemeData theme) {
    // Create nutrition stories
    final stories = NutritionStoryFactory.createNutritionStories(context);

    // Return the stories widget with WhatsApp/Instagram style
    return HomeStoriesWidget(
      stories: stories,
      height: 72,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
      title: 'Nutrition Guide',
      showTitle: true,
    );
  }

  void _openDedicatedVideoPlayer(CourseVideo video) async {
    try {
      // Find the course this video belongs to
      Course? videoCourse;
      for (final course in _courses) {
        if (_courseVideos.containsKey(course.id) &&
            _courseVideos[course.id]!.any((v) => v.id == video.id)) {
          videoCourse = course;
          break;
        }
      }

      // Navigate to comprehensive video player page (same as workout section)
      final result = await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ComprehensiveVideoPlayerPage(
            initialVideo: video,
            course: videoCourse,
            userProfile: _profile,
          ),
        ),
      );

      // Refresh data after returning from video player
      if (result != null) {
        _loadAllData();
      }
    } catch (e) {
      debugPrint('Failed to open comprehensive video player: $e');
      // Fallback to showing error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to open video player: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Helper for streak stat
  Widget _buildStreakStat(ThemeData theme, String label, int value, Color color) {
    return Column(
      children: [
        Text('$value', style: theme.textTheme.headlineSmall?.copyWith(color: color, fontWeight: FontWeight.bold)),
        Text(label, style: theme.textTheme.bodySmall?.copyWith(color: theme.hintColor)),
      ],
    );
  }

  // Helper for streak history
  Future<List<DateTime>> _getStreakHistory() async {
    try {
      // Get the singleton instance
      final dailyStreakService = DailyStreakService();

      // Ensure it's initialized
      await dailyStreakService.initialize();

      // Get current streak data
      final streak = dailyStreakService.currentStreak;
      final lastOpen = dailyStreakService.lastOpenDate;

      if (streak == 0 || lastOpen == null) return [];

      // Generate a list of the last N days for the current streak
      return List.generate(streak, (i) => lastOpen.subtract(Duration(days: streak - i - 1)));
    } catch (e) {
      print('❌ Error getting streak history: $e');
      return [];
    }
  }
}
