import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/awesome_notification_service.dart';
import '../services/workout_video_notification_service.dart';
import '../widgets/kft_app_bar.dart';
import 'notification_setup_guide_page.dart';

class NotificationSettingsPage extends ConsumerStatefulWidget {
  const NotificationSettingsPage({super.key});

  @override
  ConsumerState<NotificationSettingsPage> createState() => _NotificationSettingsPageState();
}

class _NotificationSettingsPageState extends ConsumerState<NotificationSettingsPage> {
  final EnhancedNotificationService _notificationService = EnhancedNotificationService();
  final WorkoutVideoNotificationService _videoNotificationService = WorkoutVideoNotificationService();

  bool _workoutNotificationsEnabled = true;
  String _workoutTime = '07:00';
  bool _hasPermissions = false;
  bool _isLoading = true;

  // Video notification settings
  bool _videoNotificationsEnabled = true;
  bool _newVideoNotificationsEnabled = true;
  bool _unlockNotificationsEnabled = true;
  bool _smartSuggestionsEnabled = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  // Helper methods for showing snackbars
  void _showSuccessSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).clearSnackBars();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).clearSnackBars();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  Future<void> _loadSettings() async {
    setState(() => _isLoading = true);

    try {
      print('DEBUG: Loading notification settings...');

      // Initialize the notification service first
      await _notificationService.initialize();
      print('DEBUG: Notification service initialized');

      final workoutEnabled = await _notificationService.isWorkoutNotificationEnabled();
      final workoutTime = await _notificationService.getWorkoutReminderTime();
      final hasPermissions = await _notificationService.hasPermissions();

      // Load video notification settings
      await _videoNotificationService.initialize();
      final videoNotificationsEnabled = await _videoNotificationService.areVideoNotificationsEnabled();
      final newVideoNotificationsEnabled = await _videoNotificationService.areNewVideoNotificationsEnabled();
      final unlockNotificationsEnabled = await _videoNotificationService.areUnlockNotificationsEnabled();
      final smartSuggestionsEnabled = await _videoNotificationService.areSmartSuggestionsEnabled();

      print('DEBUG: Settings loaded - Workout: $workoutEnabled, Video: $videoNotificationsEnabled, Permissions: $hasPermissions');

      setState(() {
        _workoutNotificationsEnabled = workoutEnabled;
        _workoutTime = workoutTime;
        _hasPermissions = hasPermissions;
        _videoNotificationsEnabled = videoNotificationsEnabled;
        _newVideoNotificationsEnabled = newVideoNotificationsEnabled;
        _unlockNotificationsEnabled = unlockNotificationsEnabled;
        _smartSuggestionsEnabled = smartSuggestionsEnabled;
        _isLoading = false;
      });
    } catch (e) {
      print('DEBUG: Error loading settings: $e');
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading settings: $e')),
        );
      }
    }
  }

  Future<void> _requestPermissions() async {
    final granted = await _notificationService.requestPermissions();
    setState(() => _hasPermissions = granted);

    if (granted) {
      await _notificationService.initializeNotifications();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Notification permissions granted!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Notification permissions are required for reminders to work.'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  Future<void> _toggleWorkoutNotifications(bool enabled) async {
    try {
      print('DEBUG: Toggling workout notifications to: $enabled');

      // Store original state in case we need to revert
      final originalState = _workoutNotificationsEnabled;

      // Ensure service is initialized
      await _notificationService.initialize();
      print('DEBUG: Notification service initialized');

      if (enabled) {
        // Check and request permissions first
        print('DEBUG: Checking permissions...');
        bool hasPerms = await _notificationService.hasPermissions();
        print('DEBUG: Current permissions status: $hasPerms');

        if (!hasPerms) {
          print('DEBUG: Requesting permissions...');
          hasPerms = await _notificationService.requestPermissions();
          print('DEBUG: Permission request result: $hasPerms');

          if (!hasPerms) {
            print('DEBUG: Permissions denied, cannot enable notifications');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Notification permissions are required to enable reminders'),
                  backgroundColor: Colors.orange,
                ),
              );
            }
            return; // Don't change the toggle state
          }

          // Update permissions status
          setState(() => _hasPermissions = true);
        }
      }

      print('DEBUG: Setting workout notification enabled: $enabled');

      // Update UI state optimistically
      setState(() => _workoutNotificationsEnabled = enabled);

      // Update the service
      await _notificationService.setWorkoutNotificationEnabled(enabled);

      print('DEBUG: Workout notification toggle completed successfully');
    } catch (e) {
      print('DEBUG: Error toggling workout notifications: $e');

      // Revert the state if there was an error
      setState(() => _workoutNotificationsEnabled = !enabled);

      // Re-throw to be handled by caller if needed
      rethrow;
    }
  }

  // Video notification toggle methods
  Future<void> _toggleVideoNotifications(bool enabled) async {
    try {
      // Update UI immediately for responsiveness
      setState(() => _videoNotificationsEnabled = enabled);

      // Save to storage
      await _videoNotificationService.setVideoNotificationsEnabled(enabled);

      print('🔔 Video notifications ${enabled ? 'enabled' : 'disabled'}');
    } catch (e) {
      // Revert UI state on error
      setState(() => _videoNotificationsEnabled = !enabled);
      print('❌ Error toggling video notifications: $e');
      rethrow; // Re-throw to be handled by caller if needed
    }
  }

  Future<void> _toggleNewVideoNotifications(bool enabled) async {
    try {
      // Update UI immediately for responsiveness
      setState(() => _newVideoNotificationsEnabled = enabled);

      // Save to storage
      await _videoNotificationService.setNewVideoNotificationsEnabled(enabled);

      print('🎬 New video notifications ${enabled ? 'enabled' : 'disabled'}');
    } catch (e) {
      // Revert UI state on error
      setState(() => _newVideoNotificationsEnabled = !enabled);
      print('❌ Error toggling new video notifications: $e');
    }
  }

  Future<void> _toggleUnlockNotifications(bool enabled) async {
    try {
      // Update UI immediately for responsiveness
      setState(() => _unlockNotificationsEnabled = enabled);

      // Save to storage
      await _videoNotificationService.setUnlockNotificationsEnabled(enabled);

      print('🔓 Unlock notifications ${enabled ? 'enabled' : 'disabled'}');
    } catch (e) {
      // Revert UI state on error
      setState(() => _unlockNotificationsEnabled = !enabled);
      print('❌ Error toggling unlock notifications: $e');
    }
  }

  Future<void> _toggleSmartSuggestions(bool enabled) async {
    try {
      // Update UI immediately for responsiveness
      setState(() => _smartSuggestionsEnabled = enabled);

      // Save to storage
      await _videoNotificationService.setSmartSuggestionsEnabled(enabled);

      print('🧠 Smart suggestions ${enabled ? 'enabled' : 'disabled'}');
    } catch (e) {
      // Revert UI state on error
      setState(() => _smartSuggestionsEnabled = !enabled);
      print('❌ Error toggling smart suggestions: $e');
    }
  }

  Future<void> _sendTestWorkoutReminder() async {
    try {
      await _videoNotificationService.sendTestWorkoutReminder();
      _showSuccessSnackBar('Test workout reminder sent! Check your notifications.');
    } catch (e) {
      _showErrorSnackBar('Error sending test reminder: ${e.toString()}');
    }
  }

  Future<void> _sendTestVideoNotification() async {
    try {
      await _videoNotificationService.sendTestVideoNotification();
      _showSuccessSnackBar('Test video notification sent! 🎬');
    } catch (e) {
      _showErrorSnackBar('Error sending test video notification: ${e.toString()}');
    }
  }

  Future<void> _sendTestUnlockNotification() async {
    try {
      await _videoNotificationService.sendTestUnlockNotification();
      _showSuccessSnackBar('Test unlock notification sent! 🔓');
    } catch (e) {
      _showErrorSnackBar('Error sending test unlock notification: ${e.toString()}');
    }
  }

  Future<void> _selectWorkoutTime() async {
    final timeParts = _workoutTime.split(':');
    final currentTime = TimeOfDay(
      hour: int.parse(timeParts[0]),
      minute: int.parse(timeParts[1]),
    );

    final selectedTime = await _showModernTimePicker(context, currentTime);

    if (selectedTime != null) {
      final timeString = '${selectedTime.hour.toString().padLeft(2, '0')}:${selectedTime.minute.toString().padLeft(2, '0')}';
      setState(() => _workoutTime = timeString);
      await _notificationService.setWorkoutReminderTime(timeString);

      if (mounted) {
        _showSuccessSnackBar('Workout reminder time set to ${_formatTimeDisplay(selectedTime)}');
      }
    }
  }

  Future<TimeOfDay?> _showModernTimePicker(BuildContext context, TimeOfDay initialTime) async {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    TimeOfDay selectedTime = initialTime;

    return await showDialog<TimeOfDay>(
      context: context,
      barrierDismissible: false,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            constraints: const BoxConstraints(maxWidth: 400),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(isDark ? 0.5 : 0.2),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            const Color(0xFF6366F1),
                            const Color(0xFF8B5CF6),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF6366F1).withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.access_time,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Workout Reminder Time',
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Choose when you want to be reminded',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurface.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Current Time Display
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        const Color(0xFF6366F1).withOpacity(0.1),
                        const Color(0xFF8B5CF6).withOpacity(0.05),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: const Color(0xFF6366F1).withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      Text(
                        'Selected Time',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _formatTimeDisplay(selectedTime),
                        style: theme.textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF6366F1),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getTimeRecommendation(selectedTime),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.6),
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),

                // Time Selection Buttons
                _buildTimeSelectionGrid(theme, selectedTime, (newTime) {
                  setDialogState(() {
                    selectedTime = newTime;
                  });
                }),
                const SizedBox(height: 20),

                // Custom Time Button
                Container(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: () async {
                      try {
                        // Show the native time picker with proper context handling
                        final customTime = await showTimePicker(
                          context: context,
                          initialTime: selectedTime,
                          helpText: 'Select workout reminder time',
                          cancelText: 'Cancel',
                          confirmText: 'Set Time',
                          builder: (BuildContext context, Widget? child) {
                            return Theme(
                              data: theme.copyWith(
                                timePickerTheme: TimePickerThemeData(
                                  backgroundColor: theme.colorScheme.surface,
                                  hourMinuteTextColor: theme.colorScheme.onSurface,
                                  dayPeriodTextColor: theme.colorScheme.onSurface,
                                  dialHandColor: const Color(0xFF6366F1),
                                  dialBackgroundColor: theme.colorScheme.surface,
                                  hourMinuteColor: theme.colorScheme.primaryContainer.withOpacity(0.2),
                                  dayPeriodBorderSide: BorderSide(
                                    color: const Color(0xFF6366F1).withOpacity(0.3),
                                  ),
                                  helpTextStyle: theme.textTheme.bodyMedium?.copyWith(
                                    color: theme.colorScheme.onSurface,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  entryModeIconColor: const Color(0xFF6366F1),
                                  inputDecorationTheme: InputDecorationTheme(
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide(
                                        color: const Color(0xFF6366F1).withOpacity(0.3),
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: const BorderSide(
                                        color: Color(0xFF6366F1),
                                        width: 2,
                                      ),
                                    ),
                                  ),
                                  cancelButtonStyle: TextButton.styleFrom(
                                    foregroundColor: theme.colorScheme.onSurface.withOpacity(0.7),
                                  ),
                                  confirmButtonStyle: TextButton.styleFrom(
                                    foregroundColor: const Color(0xFF6366F1),
                                    backgroundColor: const Color(0xFF6366F1).withOpacity(0.1),
                                  ),
                                ),
                              ),
                              child: child!,
                            );
                          },
                        );

                        // If user selected a time, update the dialog state
                        if (customTime != null) {
                          setDialogState(() {
                            selectedTime = customTime;
                          });
                        }
                      } catch (e) {
                        print('Error showing time picker: $e');
                        // Show error feedback to user
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Error opening time picker. Please try again.'),
                            backgroundColor: const Color(0xFFEF4444),
                          ),
                        );
                      }
                    },
                    style: OutlinedButton.styleFrom(
                      foregroundColor: const Color(0xFF6366F1),
                      side: BorderSide(
                        color: const Color(0xFF6366F1).withOpacity(0.3),
                        width: 1,
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    icon: const Icon(Icons.schedule, size: 20),
                    label: const Text(
                      'Choose Custom Time',
                      style: TextStyle(fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'Cancel',
                          style: TextStyle(
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(selectedTime),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF6366F1),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                          shadowColor: const Color(0xFF6366F1).withOpacity(0.3),
                        ),
                        child: const Text(
                          'Set Time',
                          style: TextStyle(fontWeight: FontWeight.w600),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }





  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? const Color(0xFF0A0A0A) : const Color(0xFFF8F9FA),
      appBar: const KFTAppBar(
        title: 'Notifications',
        showBackButton: true,
      ),
      body: _isLoading
        ? Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const CircularProgressIndicator(),
                ),
                const SizedBox(height: 16),
                Text(
                  'Loading notification settings...',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          )
        : CustomScrollView(
            slivers: [
              SliverPadding(
                padding: const EdgeInsets.all(20),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    // Permission Status Card
                    _buildPermissionStatusCard(theme, isDark),
                    const SizedBox(height: 24),

                    // Workout Reminders Section
                    _buildSectionHeader('Workout Reminders', Icons.fitness_center, theme),
                    const SizedBox(height: 12),
                    _buildWorkoutReminderCard(theme, isDark),
                    const SizedBox(height: 24),

                    // Workout Video Notifications Section
                    _buildSectionHeader('Workout Videos', Icons.video_library, theme),
                    const SizedBox(height: 12),
                    _buildVideoNotificationCard(theme, isDark),
                    const SizedBox(height: 24),

                    // Quick Actions
                    _buildQuickActionsCard(theme, isDark),
                    const SizedBox(height: 24),

                    // Information Card
                    _buildInfoCard(theme, isDark),
                    const SizedBox(height: 100), // Bottom padding
                  ]),
                ),
              ),
            ],
          ),
    );
  }

  Widget _buildPermissionStatusCard(ThemeData theme, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: _hasPermissions
          ? LinearGradient(
              colors: [
                const Color(0xFF4CAF50).withOpacity(0.1),
                const Color(0xFF8BC34A).withOpacity(0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            )
          : LinearGradient(
              colors: [
                const Color(0xFFFF9800).withOpacity(0.1),
                const Color(0xFFFFC107).withOpacity(0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: _hasPermissions
            ? const Color(0xFF4CAF50).withOpacity(0.2)
            : const Color(0xFFFF9800).withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _hasPermissions
                    ? const Color(0xFF4CAF50).withOpacity(0.2)
                    : const Color(0xFFFF9800).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _hasPermissions ? Icons.verified_user : Icons.warning_rounded,
                  color: _hasPermissions ? const Color(0xFF4CAF50) : const Color(0xFFFF9800),
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Notification Permissions',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _hasPermissions
                        ? 'All set! Notifications are working perfectly.'
                        : 'Permissions needed for reminders to work.',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (!_hasPermissions) ...[
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _requestPermissions,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFF9800),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                icon: const Icon(Icons.notifications_active),
                label: const Text(
                  'Enable Notifications',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon, ThemeData theme) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: theme.colorScheme.primary,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  Widget _buildWorkoutReminderCard(ThemeData theme, bool isDark) {
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(isDark ? 0.3 : 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        const Color(0xFF6366F1),
                        const Color(0xFF8B5CF6),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF6366F1).withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.fitness_center,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Daily Workout Reminders',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Stay consistent with your fitness goals',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                _buildModernToggle(
                  value: _workoutNotificationsEnabled,
                  onChanged: _toggleWorkoutNotifications,
                  activeColor: const Color(0xFF6366F1),
                ),
              ],
            ),
          ),
          if (_workoutNotificationsEnabled) ...[
            Container(
              width: double.infinity,
              height: 1,
              color: theme.colorScheme.outline.withOpacity(0.1),
            ),
            InkWell(
              onTap: _selectWorkoutTime,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: const Color(0xFF6366F1).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.access_time,
                        color: const Color(0xFF6366F1),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Reminder Time',
                            style: theme.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 6),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: const Color(0xFF6366F1).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: const Color(0xFF6366F1).withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: Text(
                              _formatTimeDisplay(TimeOfDay(
                                hour: int.parse(_workoutTime.split(':')[0]),
                                minute: int.parse(_workoutTime.split(':')[1]),
                              )),
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: const Color(0xFF6366F1),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.chevron_right,
                      color: theme.colorScheme.onSurface.withOpacity(0.5),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildVideoNotificationCard(ThemeData theme, bool isDark) {
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(isDark ? 0.3 : 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Main toggle
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        const Color(0xFF8B5CF6),
                        const Color(0xFFA855F7),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF8B5CF6).withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.video_library,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Video Notifications',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Get notified about new videos and unlocks',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                _buildModernToggle(
                  value: _videoNotificationsEnabled,
                  onChanged: _toggleVideoNotifications,
                  activeColor: const Color(0xFF8B5CF6),
                ),
              ],
            ),
          ),

          // Sub-options when enabled
          if (_videoNotificationsEnabled) ...[
            Container(
              width: double.infinity,
              height: 1,
              color: theme.colorScheme.outline.withOpacity(0.1),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // New video notifications
                  _buildVideoNotificationOption(
                    theme,
                    'New Video Releases',
                    'Get notified when new workout videos are published',
                    Icons.new_releases,
                    _newVideoNotificationsEnabled,
                    _toggleNewVideoNotifications,
                  ),
                  const SizedBox(height: 16),

                  // Unlock notifications
                  _buildVideoNotificationOption(
                    theme,
                    'Video Unlock Alerts',
                    'Receive notifications when requested videos unlock',
                    Icons.lock_open,
                    _unlockNotificationsEnabled,
                    _toggleUnlockNotifications,
                  ),
                  const SizedBox(height: 16),

                  // Smart suggestions
                  _buildVideoNotificationOption(
                    theme,
                    'Smart Workout Suggestions',
                    'Get personalized workout time recommendations',
                    Icons.psychology,
                    _smartSuggestionsEnabled,
                    _toggleSmartSuggestions,
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildVideoNotificationOption(
    ThemeData theme,
    String title,
    String description,
    IconData icon,
    bool value,
    Function(bool) onChanged,
  ) {
    final isEnabled = _videoNotificationsEnabled;
    final opacity = isEnabled ? 1.0 : 0.5;

    return Opacity(
      opacity: opacity,
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF8B5CF6).withOpacity(isEnabled ? 0.1 : 0.05),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: const Color(0xFF8B5CF6).withOpacity(isEnabled ? 1.0 : 0.5),
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface.withOpacity(isEnabled ? 1.0 : 0.5),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(isEnabled ? 0.6 : 0.3),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          Switch(
            value: value,
            onChanged: isEnabled ? onChanged : null, // Disable if main toggle is off
            activeColor: const Color(0xFF8B5CF6),
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ],
      ),
    );
  }

  Widget _buildModernToggle({
    required bool value,
    required ValueChanged<bool> onChanged,
    required Color activeColor,
  }) {
    return GestureDetector(
      onTap: () => onChanged(!value),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: 56,
        height: 32,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: value ? activeColor : Colors.grey.withOpacity(0.3),
          boxShadow: value ? [
            BoxShadow(
              color: activeColor.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ] : null,
        ),
        child: AnimatedAlign(
          duration: const Duration(milliseconds: 200),
          alignment: value ? Alignment.centerRight : Alignment.centerLeft,
          child: Container(
            width: 28,
            height: 28,
            margin: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(14),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: value ? Icon(
              Icons.check,
              color: activeColor,
              size: 16,
            ) : null,
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActionsCard(ThemeData theme, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(isDark ? 0.3 : 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.flash_on,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Quick Actions',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildQuickActionButton(
                  'Enable All',
                  Icons.notifications_active,
                  () => _enableAllNotifications(),
                  theme,
                  const Color(0xFF10B981),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQuickActionButton(
                  'Test Reminder',
                  Icons.send,
                  () => _sendTestWorkoutReminder(),
                  theme,
                  const Color(0xFF6366F1),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQuickActionButton(
                  'Disable All',
                  Icons.notifications_off,
                  () => _disableAllNotifications(),
                  theme,
                  const Color(0xFFEF4444),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildQuickActionButton(
                  'Test Video',
                  Icons.video_library,
                  () => _sendTestVideoNotification(),
                  theme,
                  const Color(0xFF8B5CF6),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQuickActionButton(
                  'Test Unlock',
                  Icons.lock_open,
                  () => _sendTestUnlockNotification(),
                  theme,
                  const Color(0xFFFF8A65),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionButton(
    String label,
    IconData icon,
    VoidCallback onTap,
    ThemeData theme,
    Color color,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard(ThemeData theme, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primaryContainer.withOpacity(0.3),
            theme.colorScheme.primaryContainer.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.lightbulb_outline,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Text(
                'About Notifications',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ..._buildInfoItems(theme),
        ],
      ),
    );
  }

  List<Widget> _buildInfoItems(ThemeData theme) {
    final items = [
      'Workout reminders help maintain consistent exercise routines',
      'All notifications can be customized or disabled anytime',
      'Notifications work even when the app is closed',
      'Stay motivated with timely fitness reminders',
    ];

    return items.map((item) => Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: const EdgeInsets.only(top: 8, right: 12),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              borderRadius: BorderRadius.circular(3),
            ),
          ),
          Expanded(
            child: Text(
              item,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.8),
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }

  Future<void> _enableAllNotifications() async {
    if (!_hasPermissions) {
      await _requestPermissions();
      if (!_hasPermissions) return;
    }

    try {
      // Enable workout reminders
      await _toggleWorkoutNotifications(true);

      // Enable video notifications and all sub-options
      await _toggleVideoNotifications(true);
      await _toggleNewVideoNotifications(true);
      await _toggleUnlockNotifications(true);
      await _toggleSmartSuggestions(true);

      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        _showSuccessSnackBar('All notifications enabled successfully! 🔔');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        _showErrorSnackBar('Error enabling notifications: ${e.toString()}');
      }
    }
  }

  Future<void> _disableAllNotifications() async {
    try {
      // Disable workout reminders
      await _toggleWorkoutNotifications(false);

      // Disable video notifications and all sub-options
      await _toggleVideoNotifications(false);
      await _toggleNewVideoNotifications(false);
      await _toggleUnlockNotifications(false);
      await _toggleSmartSuggestions(false);

      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        _showSuccessSnackBar('All notifications disabled successfully! 🔕');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        _showErrorSnackBar('Error disabling notifications: ${e.toString()}');
      }
    }
  }

  Widget _buildTimeSelectionGrid(ThemeData theme, TimeOfDay selectedTime, Function(TimeOfDay) onTimeSelected) {
    final popularTimes = [
      {'time': const TimeOfDay(hour: 6, minute: 0), 'label': '6:00 AM', 'description': 'Early Bird'},
      {'time': const TimeOfDay(hour: 8, minute: 0), 'label': '8:00 AM', 'description': 'Morning'},
      {'time': const TimeOfDay(hour: 12, minute: 0), 'label': '12:00 PM', 'description': 'Lunch Break'},
      {'time': const TimeOfDay(hour: 17, minute: 0), 'label': '5:00 PM', 'description': 'After Work'},
      {'time': const TimeOfDay(hour: 19, minute: 0), 'label': '7:00 PM', 'description': 'Evening'},
      {'time': const TimeOfDay(hour: 21, minute: 0), 'label': '9:00 PM', 'description': 'Night'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Popular Times',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 12),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 2.5,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
          ),
          itemCount: popularTimes.length,
          itemBuilder: (context, index) {
            final timeData = popularTimes[index];
            final time = timeData['time'] as TimeOfDay;
            final isSelected = time.hour == selectedTime.hour && time.minute == selectedTime.minute;

            return InkWell(
              onTap: () => onTimeSelected(time),
              borderRadius: BorderRadius.circular(12),
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isSelected
                    ? const Color(0xFF6366F1).withOpacity(0.1)
                    : theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected
                      ? const Color(0xFF6366F1)
                      : theme.colorScheme.outline.withOpacity(0.2),
                    width: isSelected ? 2 : 1,
                  ),
                  boxShadow: isSelected ? [
                    BoxShadow(
                      color: const Color(0xFF6366F1).withOpacity(0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ] : null,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      timeData['label'] as String,
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isSelected
                          ? const Color(0xFF6366F1)
                          : theme.colorScheme.onSurface,
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 1),
                    Text(
                      timeData['description'] as String,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                        fontSize: 9,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  String _formatTimeDisplay(TimeOfDay time) {
    final now = DateTime.now();
    final dateTime = DateTime(now.year, now.month, now.day, time.hour, time.minute);
    final is24Hour = MediaQuery.of(context).alwaysUse24HourFormat;

    if (is24Hour) {
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    } else {
      return time.format(context);
    }
  }

  String _getTimeRecommendation(TimeOfDay time) {
    if (time.hour >= 5 && time.hour < 8) {
      return 'Perfect for early morning workouts';
    } else if (time.hour >= 8 && time.hour < 12) {
      return 'Great for morning energy boost';
    } else if (time.hour >= 12 && time.hour < 17) {
      return 'Good for afternoon sessions';
    } else if (time.hour >= 17 && time.hour < 20) {
      return 'Ideal for after-work fitness';
    } else if (time.hour >= 20 && time.hour < 24) {
      return 'Perfect for evening workouts';
    } else if (time.hour >= 0 && time.hour < 5) {
      return 'Late night workout session';
    } else {
      return 'Your personalized workout time';
    }
  }


}
