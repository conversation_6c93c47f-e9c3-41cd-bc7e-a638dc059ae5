import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import '../models/user_profile.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../widgets/progress_card_new.dart';
import '../design_system/kft_design_system.dart';
import '../services/daily_streak_service.dart';
import '../services/progress_service.dart';
import '../services/user_service.dart';
import '../services/video_streak_service.dart';

class ProgressPageNew extends StatefulWidget {
  final UserProfile? user;

  const ProgressPageNew({Key? key, this.user}) : super(key: key);

  @override
  State<ProgressPageNew> createState() => _ProgressPageNewState();
}

class _ProgressPageNewState extends State<ProgressPageNew> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late ProgressService _progressService;
  late DailyStreakService _dailyStreakService;

  bool _isRefreshing = false;
  bool _isInitialLoading = true;
  int _currentStreak = 0;
  int _highestStreak = 0;
  int _todayVideoCount = 0;
  int _todayVideoMinutes = 0;
  List<Map<String, dynamic>> _weeklyVideoStats = [];
  List<Map<String, dynamic>> _recentVideos = [];
  int _totalVideosWatched = 0;
  int _totalVideoMinutes = 0;
  double _avgVideoSessionMinutes = 0;
  StreamSubscription<OverallStats>? _statsSub;
  StreamSubscription<Map<int, VideoProgress>>? _videoProgressSub;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Initialize services
    _progressService = ProgressService();
    _dailyStreakService = DailyStreakService();

    // Initialize data loading
    _initializeData();
  }

  /// Initialize all data and set up real-time listeners
  Future<void> _initializeData() async {
    try {
      print('🔄 Initializing Progress Page data...');

      // Initialize services first
      await _progressService.initialize();
      await _dailyStreakService.initialize();

      // Set up real-time listeners
      _setupStreamListeners();

      // Load initial data
      await _loadAllData();

      setState(() {
        _isInitialLoading = false;
      });

      print('✅ Progress Page data initialized successfully');
    } catch (e) {
      print('❌ Error initializing Progress Page data: $e');
      setState(() {
        _isInitialLoading = false;
      });
    }
  }

  /// Set up stream listeners for real-time updates
  void _setupStreamListeners() {
    _statsSub = _progressService.overallStatsStream.listen((stats) {
      if (mounted) {
        setState(() {
          _totalVideosWatched = stats.totalVideosWatched;
          _totalVideoMinutes = stats.totalWatchTimeSeconds ~/ 60;
          _avgVideoSessionMinutes = _totalVideosWatched > 0 ? _totalVideoMinutes / _totalVideosWatched : 0;
        });
        print('📊 Real-time stats update: $_totalVideosWatched videos, $_totalVideoMinutes minutes');
      }
    });

    _videoProgressSub = _progressService.videoProgressStream.listen((progressMap) async {
      if (mounted) {
        try {
          // Update recent videos with metadata
          final recentVideosWithMeta = await _progressService.getRecentVideosWithMetadata(limit: 5);
          final todayStats = _progressService.getTodayVideoStats();
          final weeklyStats = _progressService.getWeeklyVideoStats();

          setState(() {
            _recentVideos = recentVideosWithMeta;
            _todayVideoCount = todayStats['count'] ?? 0;
            _todayVideoMinutes = todayStats['minutes'] ?? 0;
            _weeklyVideoStats = weeklyStats;
          });

          print('📹 Real-time video progress update: $_todayVideoCount videos today, $_todayVideoMinutes minutes');
        } catch (e) {
          print('❌ Error updating video progress: $e');
        }
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _statsSub?.cancel();
    _videoProgressSub?.cancel();
    super.dispose();
  }

  /// Load all data (streaks, video stats, etc.)
  Future<void> _loadAllData() async {
    try {
      // Load data in parallel for better performance
      await Future.wait([
        _loadStreaks(),
        _loadVideoStats(),
      ]);
      print('✅ All data loaded successfully');
    } catch (e) {
      print('❌ Error loading all data: $e');
    }
  }

  Future<void> _refreshData() async {
    setState(() {
      _isRefreshing = true;
    });

    try {
      // Force refresh services
      await _progressService.refreshAllProgress();
      await _dailyStreakService.updateStreakOnAppOpen();

      // Reload all data
      await _loadAllData();

      print('🔄 Data refreshed successfully');
    } catch (e) {
      print('❌ Error refreshing data: $e');
    } finally {
      setState(() {
        _isRefreshing = false;
      });
    }
  }

  Future<void> _loadStreaks() async {
    try {
      // Update streak on app open (this handles the daily logic)
      await _dailyStreakService.updateStreakOnAppOpen();

      if (mounted) {
        setState(() {
          _currentStreak = _dailyStreakService.currentStreak;
          _highestStreak = _dailyStreakService.highestStreak;
        });
      }

      print('🔥 Streaks loaded - Current: $_currentStreak, Highest: $_highestStreak');
    } catch (e) {
      print('❌ Error loading streaks: $e');
      // Fallback to video streak service
      try {
        final videoStreakService = VideoStreakService();
        final currentStreak = await videoStreakService.getCurrentStreak();
        final highestStreak = await videoStreakService.getHighestStreak();
        if (mounted) {
          setState(() {
            _currentStreak = currentStreak;
            _highestStreak = highestStreak;
          });
        }
      } catch (e2) {
        print('❌ Error loading video streaks: $e2');
      }
    }
  }

  Future<void> _loadVideoStats() async {
    try {
      final todayStats = _progressService.getTodayVideoStats();
      final weeklyStats = _progressService.getWeeklyVideoStats();
      final recentVideos = await _progressService.getRecentVideosWithMetadata(limit: 5);
      final overall = _progressService.overallStats;

      if (mounted) {
        setState(() {
          _todayVideoCount = todayStats['count'] ?? 0;
          _todayVideoMinutes = todayStats['minutes'] ?? 0;
          _weeklyVideoStats = weeklyStats;
          _recentVideos = recentVideos;
          _totalVideosWatched = overall.totalVideosWatched;
          _totalVideoMinutes = overall.totalWatchTimeSeconds ~/ 60;
          _avgVideoSessionMinutes = _totalVideosWatched > 0 ? _totalVideoMinutes / _totalVideosWatched : 0;
        });
      }

      print('📊 Video stats loaded - Today: $_todayVideoCount videos, $_todayVideoMinutes minutes');
      print('📊 Total: $_totalVideosWatched videos, $_totalVideoMinutes minutes');
      print('📊 Weekly stats: ${_weeklyVideoStats.length} days');
    } catch (e) {
      print('❌ Error loading video stats: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Your Progress'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.transparent,
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: isDarkMode ? Brightness.light : Brightness.dark,
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share_outlined),
            onPressed: () => _showShareProgressDialog(context),
            tooltip: 'Share Progress',
          ),
        ],
      ),
      body: _isInitialLoading
        ? _buildLoadingState(context)
        : RefreshIndicator(
            onRefresh: _refreshData,
            child: CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                // Summary Cards
                SliverToBoxAdapter(
                  child: _buildSummarySection(context),
                ),

                // Tab Bar
                SliverPersistentHeader(
                  pinned: true,
                  delegate: _SliverAppBarDelegate(
                    TabBar(
                      controller: _tabController,
                      labelColor: theme.colorScheme.primary,
                      unselectedLabelColor: isDarkMode ? Colors.white60 : Colors.black54,
                      indicatorColor: theme.colorScheme.primary,
                      indicatorWeight: 3,
                      tabs: const [
                        Tab(text: 'Activity'),
                        Tab(text: 'Stats'),
                        Tab(text: 'Achievements'),
                      ],
                    ),
                  ),
                ),

                // Tab Content
                SliverFillRemaining(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildActivityTab(context),
                      _buildStatsTab(context),
                      _buildAchievementsTab(context),
                    ],
                  ),
                ),
              ],
            ),
          ),
    );
  }

  /// Build loading state with skeleton UI
  Widget _buildLoadingState(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Loading greeting
          Container(
            height: 24,
            width: 200,
            decoration: BoxDecoration(
              color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            height: 16,
            width: 150,
            decoration: BoxDecoration(
              color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 24),

          // Loading summary cards
          Row(
            children: List.generate(3, (index) => Expanded(
              child: Container(
                margin: EdgeInsets.only(right: index < 2 ? 12 : 0),
                height: 100,
                decoration: BoxDecoration(
                  color: isDarkMode ? Colors.grey.shade800 : Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
            )),
          ),
          const SizedBox(height: 32),

          // Loading message
          Center(
            child: Column(
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  'Loading your progress...',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onBackground.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection(BuildContext context) {
    final theme = Theme.of(context);
    final currentStreak = _currentStreak;
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Greeting and Motivation
          Text(
            'Hello, ${widget.user?.name ?? 'there'}!',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            _getMotivationalText(currentStreak),
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onBackground.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 24),

          // Summary Cards
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  context,
                  title: 'Today',
                  value: '$_todayVideoCount',
                  subtitle: 'Videos',
                  icon: Icons.ondemand_video,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSummaryCard(
                  context,
                  title: 'Active',
                  value: '$_todayVideoMinutes',
                  subtitle: 'Minutes',
                  icon: Icons.timer,
                  color: Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSummaryCard(
                  context,
                  title: 'Streak',
                  value: '$_currentStreak',
                  subtitle: 'Days',
                  icon: Icons.local_fire_department,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
    BuildContext context, {
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
  }) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey.shade800 : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                title,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onBackground.withOpacity(0.7),
                ),
              ),
              const Spacer(),
              Icon(
                icon,
                color: color,
                size: 16,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            subtitle,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onBackground.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  // Activity Tab Content
  Widget _buildActivityTab(BuildContext context) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildWeeklyActivityChart(context),
        const SizedBox(height: 24),
        _buildRecentWorkouts(context),
      ],
    );
  }

  Widget _buildWeeklyActivityChart(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final now = DateTime.now();
    if (_weeklyVideoStats.isEmpty) {
      return Tooltip(
        message: 'Your weekly video activity will appear here as you watch more videos.',
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: isDarkMode ? Colors.grey.shade800 : Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Weekly Activity',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),
              Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.ondemand_video,
                      size: 48,
                      color: theme.colorScheme.onBackground.withOpacity(0.3),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No video activity this week',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onBackground.withOpacity(0.7),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Watch videos to see your weekly progress here',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onBackground.withOpacity(0.5),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }
    final days = _weeklyVideoStats.map((e) => e['date'] as DateTime).toList();
    final videosPerDay = _weeklyVideoStats.map((e) => e['count'] as int).toList();
    final minutesPerDay = _weeklyVideoStats.map((e) => e['minutes'] as int).toList();
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey.shade800 : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Weekly Activity',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          SizedBox(
            height: 200,
            child: BarChart(
              BarChartData(
                alignment: BarChartAlignment.spaceAround,
                maxY: (videosPerDay.isNotEmpty ? videosPerDay.reduce((a, b) => a > b ? a : b).toDouble() + 2.0 : 4.0).clamp(4.0, 10.0),
                barTouchData: BarTouchData(
                  enabled: true,
                  touchTooltipData: BarTouchTooltipData(
                    tooltipBgColor: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade200,
                    tooltipRoundedRadius: 8,
                    tooltipPadding: const EdgeInsets.all(8),
                    tooltipMargin: 8,
                    getTooltipItem: (group, groupIndex, rod, rodIndex) {
                      return BarTooltipItem(
                        '${videosPerDay[groupIndex]} videos\n${minutesPerDay[groupIndex]} mins',
                        TextStyle(
                          color: isDarkMode ? Colors.white : Colors.black,
                          fontWeight: FontWeight.bold,
                        ),
                      );
                    },
                  ),
                ),
                titlesData: FlTitlesData(
                  show: true,
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        int idx = value.toInt();
                        if (idx < 0 || idx > 6) return const SizedBox.shrink();
                        return Padding(
                          padding: const EdgeInsets.only(top: 8),
                          child: Text(
                            DateFormat('E').format(days[idx]),
                            style: theme.textTheme.bodySmall,
                          ),
                        );
                      },
                      reservedSize: 30,
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        if (value == 0) return const SizedBox.shrink();
                        return Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: Text(
                            value.toInt().toString(),
                            style: theme.textTheme.bodySmall,
                          ),
                        );
                      },
                      reservedSize: 30,
                    ),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                ),
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: false,
                  horizontalInterval: 1,
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
                      strokeWidth: 1,
                      dashArray: [5, 5],
                    );
                  },
                ),
                borderData: FlBorderData(show: false),
                barGroups: List.generate(7, (i) {
                  return BarChartGroupData(
                    x: i,
                    barRods: [
                      BarChartRodData(
                        toY: videosPerDay[i].toDouble(),
                        color: _isSameDay(days[i], now)
                            ? theme.colorScheme.primary
                            : theme.colorScheme.primary.withOpacity(0.7),
                        width: 16,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(6),
                          topRight: Radius.circular(6),
                        ),
                        backDrawRodData: BackgroundBarChartRodData(
                          show: true,
                          toY: (videosPerDay.isNotEmpty ? videosPerDay.reduce((a, b) => a > b ? a : b).toDouble() + 2.0 : 4.0).clamp(4.0, 10.0),
                          color: isDarkMode ? Colors.grey.shade700.withOpacity(0.2) : Colors.grey.shade200,
                        ),
                      ),
                    ],
                  );
                }),
              ),
              swapAnimationDuration: const Duration(milliseconds: 500),
              swapAnimationCurve: Curves.easeInOut,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentWorkouts(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    if (_recentVideos.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isDarkMode ? Colors.grey.shade800 : Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Videos',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.ondemand_video,
                    size: 48,
                    color: theme.colorScheme.onBackground.withOpacity(0.3),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No videos watched yet',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onBackground.withOpacity(0.7),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Watch your first video to see it here',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onBackground.withOpacity(0.5),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey.shade800 : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recent Videos',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ..._recentVideos.map((videoData) => _buildVideoItem(context, videoData)).toList(),
        ],
      ),
    );
  }

  Widget _buildVideoItem(BuildContext context, Map<String, dynamic> videoData) {
    final theme = Theme.of(context);
    final vp = videoData['videoProgress'] as VideoProgress;
    final title = videoData['title'] as String;
    final thumbnail = videoData['thumbnail'] as String?;
    final courseName = videoData['courseName'] as String;
    final watchTimeMinutes = videoData['watchTimeMinutes'] as int;
    final isCompleted = videoData['isCompleted'] as bool;
    final progressPercentage = videoData['progressPercentage'] as double;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.brightness == Brightness.dark ? Colors.grey.shade700 : Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
        border: isCompleted ? Border.all(color: Colors.green.withOpacity(0.3), width: 1) : null,
      ),
      child: Row(
        children: [
          // Video thumbnail or icon
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: thumbnail != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.network(
                      thumbnail,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Icon(
                        Icons.ondemand_video,
                        color: theme.colorScheme.primary,
                        size: 24,
                      ),
                    ),
                  )
                : Icon(
                    Icons.ondemand_video,
                    color: theme.colorScheme.primary,
                    size: 24,
                  ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  courseName,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.primary.withOpacity(0.8),
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  DateFormat('MMM dd, yyyy – HH:mm').format(vp.lastUpdated),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onBackground.withOpacity(0.6),
                  ),
                ),
                if (progressPercentage > 0 && progressPercentage < 100) ...[
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: progressPercentage / 100,
                    backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                    valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(width: 12),
          Column(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                decoration: BoxDecoration(
                  color: isCompleted
                      ? Colors.green.withOpacity(0.1)
                      : theme.colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '$watchTimeMinutes min',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: isCompleted ? Colors.green : theme.colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              if (isCompleted) ...[
                const SizedBox(height: 4),
                Icon(
                  Icons.check_circle,
                  color: Colors.green,
                  size: 16,
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  // Stats Tab Content
  Widget _buildStatsTab(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final hours = _totalVideoMinutes ~/ 60;
    final minutes = _totalVideoMinutes % 60;
    final totalTimeText = hours > 0
        ? '$hours hr ${minutes > 0 ? '$minutes min' : ''}'
        : '$minutes min';
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: isDarkMode ? Colors.grey.shade800 : Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Video Statistics',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatColumn(
                    context,
                    '$_totalVideosWatched',
                    'Total Videos',
                    Icons.ondemand_video,
                    theme.colorScheme.primary,
                  ),
                  _buildStatColumn(
                    context,
                    totalTimeText,
                    'Total Time',
                    Icons.timer,
                    Colors.green,
                  ),
                  _buildStatColumn(
                    context,
                    '${_avgVideoSessionMinutes.toStringAsFixed(1)} min',
                    'Avg. Session',
                    Icons.show_chart,
                    Colors.orange,
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),
        _buildBMIStats(context),
      ],
    );
  }

  Widget _buildStatColumn(
    BuildContext context,
    String value,
    String label,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Column(
      children: [
        Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onBackground.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildBMIStats(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // Get BMI data
    final currentBMI = widget.user?.currentBMI ?? 0.0;
    final bmiCategory = widget.user?.bmiCategory ?? 'Unknown';
    final weight = widget.user?.weight ?? 0.0;

    // Get BMI category color
    Color categoryColor;
    if (currentBMI < 18.5) {
      categoryColor = Colors.blue;
    } else if (currentBMI < 25) {
      categoryColor = Colors.green;
    } else if (currentBMI < 30) {
      categoryColor = Colors.orange;
    } else {
      categoryColor = Colors.red;
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey.shade800 : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Body Metrics',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildBMIIndicator(context, currentBMI, bmiCategory, categoryColor),
              _buildWeightDisplay(context, weight),
            ],
          ),
          const SizedBox(height: 16),
          OutlinedButton.icon(
            onPressed: () {
              _showUpdateWeightDialog(context);
            },
            icon: const Icon(Icons.edit),
            label: const Text('Update Weight'),
            style: OutlinedButton.styleFrom(
              foregroundColor: theme.colorScheme.primary,
              side: BorderSide(color: theme.colorScheme.primary),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Achievements Tab Content
  Widget _buildAchievementsTab(BuildContext context) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildAchievementsGrid(context),
      ],
    );
  }

  String _getMotivationalText(int streak) {
    if (streak == 0) {
      return 'Start your fitness journey today!';
    } else if (streak == 1) {
      return 'Great start! Keep going for a streak!';
    } else if (streak < 5) {
      return 'You\'re on a $streak-day streak. Keep it up!';
    } else if (streak < 10) {
      return 'Impressive $streak-day streak! You\'re building a habit!';
    } else {
      return 'Amazing $streak-day streak! You\'re unstoppable!';
    }
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  Widget _buildBMIIndicator(BuildContext context, double bmi, String category, Color color) {
    final theme = Theme.of(context);

    return Column(
      children: [
        Stack(
          alignment: Alignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: color.withOpacity(0.1),
                border: Border.all(
                  color: color,
                  width: 3,
                ),
              ),
            ),
            Column(
              children: [
                Text(
                  bmi.toStringAsFixed(1),
                  style: theme.textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  'BMI',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onBackground.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Text(
            category,
            style: theme.textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildWeightDisplay(BuildContext context, double weight) {
    final theme = Theme.of(context);

    return Column(
      children: [
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.purple.withOpacity(0.1),
            border: Border.all(
              color: Colors.purple,
              width: 3,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                weight.toStringAsFixed(1),
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.purple,
                ),
              ),
              Text(
                'kg',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onBackground.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.purple.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Text(
            'Current Weight',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.purple,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAchievementsGrid(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey.shade800 : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Achievements',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          GridView.count(
            crossAxisCount: 3,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            childAspectRatio: 0.85,
            children: [
              _buildAchievementBadge(
                context,
                label: 'First Workout',
                icon: Icons.emoji_events,
                achieved: (widget.user?.totalWorkouts ?? 0) > 0,
                description: 'Complete your first workout',
              ),
              _buildAchievementBadge(
                context,
                label: '10 Days Streak',
                icon: Icons.star,
                achieved: (widget.user?.currentStreak ?? 0) >= 10,
                description: 'Workout 10 days in a row',
              ),
              _buildAchievementBadge(
                context,
                label: '500 min',
                icon: Icons.timer,
                achieved: (widget.user?.totalWorkoutTime ?? 0) >= 500,
                description: 'Accumulate 500 minutes of workouts',
              ),
              _buildAchievementBadge(
                context,
                label: '10 Workouts',
                icon: Icons.fitness_center,
                achieved: (widget.user?.totalWorkouts ?? 0) >= 10,
                description: 'Complete 10 workouts',
              ),
              _buildAchievementBadge(
                context,
                label: 'Healthy BMI',
                icon: Icons.favorite,
                achieved: widget.user?.bmiCategory == 'Normal',
                description: 'Achieve a healthy BMI',
              ),
              _buildAchievementBadge(
                context,
                label: '30 Days',
                icon: Icons.calendar_today,
                achieved: (widget.user?.currentStreak ?? 0) >= 30,
                description: 'Workout for 30 days in a row',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAchievementBadge(
    BuildContext context, {
    required String label,
    required IconData icon,
    required bool achieved,
    required String description,
  }) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Tooltip(
      message: description,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 250),
        curve: Curves.easeOut,
        decoration: BoxDecoration(
          color: achieved
              ? theme.colorScheme.primary.withOpacity(0.12)
              : (isDarkMode ? Colors.grey.shade700 : Colors.grey.shade200),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: achieved ? theme.colorScheme.primary : Colors.transparent,
            width: achieved ? 2 : 0,
          ),
          boxShadow: achieved
              ? [
                  BoxShadow(
                    color: theme.colorScheme.primary.withOpacity(0.18),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : [],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: achieved
                    ? theme.colorScheme.primary.withOpacity(0.2)
                    : (isDarkMode ? Colors.grey.shade800 : Colors.grey.shade300),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: achieved
                    ? theme.colorScheme.primary
                    : (isDarkMode ? Colors.grey.shade500 : Colors.grey.shade600),
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: achieved ? FontWeight.bold : FontWeight.normal,
                color: achieved
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onBackground.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showUpdateWeightDialog(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final currentWeight = widget.user?.weight ?? 70;
    final TextEditingController weightController = TextEditingController(text: currentWeight.toString());

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Update Weight'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Enter your current weight in kg',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onBackground.withOpacity(0.7),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: weightController,
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                decoration: InputDecoration(
                  labelText: 'Weight (kg)',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  suffixText: 'kg',
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                final newWeight = double.tryParse(weightController.text);
                if (newWeight != null && newWeight > 0) {
                  // Update weight in user profile and refresh stats
                  await UserService().updateWeight(newWeight);
                  await _refreshData();
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Weight updated to ${newWeight.toStringAsFixed(1)} kg'),
                      backgroundColor: theme.colorScheme.primary,
                    ),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please enter a valid weight'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Update'),
            ),
          ],
        );
      },
    );
  }

  void _showShareProgressDialog(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // Get achievements
    final achievements = _getAchievements(widget.user);

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          contentPadding: const EdgeInsets.all(16),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Share Your Progress',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  children: [
                    CircleAvatar(
                      radius: 40,
                      backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                      child: Text(
                        widget.user?.name.isNotEmpty == true
                            ? widget.user!.name.substring(0, 1).toUpperCase()
                            : 'U',
                        style: theme.textTheme.headlineMedium?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      widget.user?.name ?? 'User',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Fitness Journey Stats',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onBackground.withOpacity(0.7),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildShareStat(
                          context,
                          '${widget.user?.totalWorkouts ?? 0}',
                          'Workouts',
                        ),
                        _buildShareStat(
                          context,
                          '${widget.user?.currentStreak ?? 0}',
                          'Day Streak',
                        ),
                        _buildShareStat(
                          context,
                          '${widget.user?.totalWorkoutTime ?? 0}',
                          'Minutes',
                        ),
                      ],
                    ),
                    if (achievements.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      const Divider(),
                      const SizedBox(height: 16),
                      Text(
                        'Achievements',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        alignment: WrapAlignment.center,
                        spacing: 8,
                        runSpacing: 8,
                        children: achievements
                            .take(3)
                            .map(
                              (a) => Container(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: theme.colorScheme.primary.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Text(
                                  a,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            )
                            .toList(),
                      ),
                    ],
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: OutlinedButton.styleFrom(
                        foregroundColor: theme.colorScheme.primary,
                        side: BorderSide(color: theme.colorScheme.primary),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.of(context).pop();
                        _shareProgress();
                      },
                      icon: const Icon(Icons.share),
                      label: const Text('Share'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildShareStat(BuildContext context, String value, String label) {
    final theme = Theme.of(context);

    return Column(
      children: [
        Text(
          value,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onBackground.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  List<String> _getAchievements(UserProfile? user) {
    if (user == null) return [];

    final achievements = <String>[];

    if (user.totalWorkouts > 0) {
      achievements.add('First Workout');
    }

    if (user.totalWorkouts >= 10) {
      achievements.add('10 Workouts');
    }

    if (user.currentStreak >= 10) {
      achievements.add('10 Days Streak');
    }

    if (user.currentStreak >= 30) {
      achievements.add('30 Days Streak');
    }

    if (user.totalWorkoutTime >= 500) {
      achievements.add('500 Minutes');
    }

    if (user.bmiCategory == 'Normal') {
      achievements.add('Healthy BMI');
    }

    return achievements;
  }

  Future<void> _shareProgress() async {
    final achievements = _getAchievements(widget.user);
    final shareText = '''
🏋️‍♂️ My Fitness Journey Stats 🏋️‍♂️

👤 ${widget.user?.name ?? 'User'}
💪 Workouts: ${widget.user?.totalWorkouts ?? 0}
🔥 Current Streak: ${widget.user?.currentStreak ?? 0} days
⏱️ Total Workout Time: ${widget.user?.totalWorkoutTime ?? 0} minutes
${achievements.isNotEmpty ? '\n🏆 Achievements: ${achievements.join(', ')}' : ''}

#FitnessJourney #WorkoutProgress
''';

    await Share.share(shareText);
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar _tabBar;

  _SliverAppBarDelegate(this._tabBar);

  @override
  double get minExtent => _tabBar.preferredSize.height;

  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      color: isDarkMode ? KFTDesignSystem.darkSurfaceColor : Colors.white,
      child: _tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return false;
  }
}
