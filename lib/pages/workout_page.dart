import 'dart:ui';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/course.dart';
import '../models/course_video.dart';
import '../services/api_service.dart';
import '../services/course_tracking_service.dart';
import 'video_player_page.dart';
import '../design_system/kft_design_system.dart';
import '../widgets/kft_button.dart';
import '../utils/animations.dart';
import '../widgets/premium_header.dart';
import '../widgets/premium_card.dart';
import '../utils/video_overlay_helper.dart';
import '../widgets/theme_toggle_button.dart';
import '../widgets/notify_me_button.dart';
import 'dedicated_video_player_page.dart';
import 'comprehensive_video_player_page.dart';
import '../models/user_profile.dart';

class WorkoutPage extends StatefulWidget {
  const WorkoutPage({Key? key}) : super(key: key);

  @override
  _WorkoutPageState createState() => _WorkoutPageState();
}

class _WorkoutPageState extends State<WorkoutPage> with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  final ApiService _apiService = ApiService();
  final CourseTrackingService _trackingService = CourseTrackingService();
  bool _isLoading = true;
  String _errorMessage = '';
  List<Course> _assignedCourses = [];
  Map<int, List<CourseVideo>> _courseVideos = {};
  Map<int, bool> _expandedCourses = {};

  // Course selection and tracking
  Course? _selectedCourse;
  Course? _lastOpenedCourse;
  CourseVideo? _lastAccessedVideo;

  // Auto-refresh functionality
  Timer? _autoRefreshTimer;
  bool _isAutoRefreshing = false;
  DateTime? _lastRefreshTime;

  // Week status tracking
  Map<int, String> _weekStatus = {}; // 'completed', 'current', 'locked'

  // Filter options for videos
  String _selectedFilter = 'All'; // 'All', 'Unlocked', 'Completed'

  // Keys for storing preferences
  static const String _selectedCourseIdKey = 'selected_workout_course_id';

  @override
  void initState() {
    super.initState();
    _loadAssignedCourses();
    _loadLastOpenedCourse();
    _loadSelectedCourse();
    _startAutoRefreshTimer();

    // Listen for app lifecycle changes to refresh when returning from video
    WidgetsBinding.instance.addObserver(this);
  }

  // Load the selected course from SharedPreferences
  Future<void> _loadSelectedCourse() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final selectedCourseId = prefs.getInt(_selectedCourseIdKey);

      if (selectedCourseId != null && _assignedCourses.isNotEmpty) {
        // Find the course with the saved ID
        final course = _assignedCourses.firstWhere(
          (c) => c.id == selectedCourseId,
          orElse: () => _assignedCourses.first,
        );

        if (mounted) {
          setState(() {
            _selectedCourse = course;
          });
        }
      }
    } catch (e) {
      print('Error loading selected course: $e');
    }
  }

  // Save the selected course to SharedPreferences
  Future<void> _saveSelectedCourse(Course course) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_selectedCourseIdKey, course.id);

      if (mounted) {
        setState(() {
          _selectedCourse = course;
        });
      }
    } catch (e) {
      print('Error saving selected course: $e');
    }
  }

  @override
  void dispose() {
    _autoRefreshTimer?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  // Handle app lifecycle changes to refresh data when returning from video
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      // App came back to foreground - force refresh to get updated stats
      debugPrint('App resumed - forcing refresh of workout stats');
      _forceRefreshStats();
    }
  }

  // Force refresh stats immediately when returning from video
  Future<void> _forceRefreshStats() async {
    if (!mounted || _isLoading) return;

    try {
      // Force refresh course stats
      await _refreshCourseStats();

      // Force refresh video progress for all courses
      for (final course in _assignedCourses) {
        await _refreshCourseVideoProgress(course.id);
      }

      debugPrint('Force refresh completed - stats updated');
    } catch (e) {
      debugPrint('Error during force refresh: $e');
    }
  }

  // Start auto-refresh timer for video progress updates
  void _startAutoRefreshTimer() {
    _autoRefreshTimer?.cancel();
    _autoRefreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted && !_isLoading) {
        _autoRefreshVideoProgress();
      }
    });
  }

  // Auto-refresh video progress and course stats without showing loading indicators
  Future<void> _autoRefreshVideoProgress() async {
    if (_isAutoRefreshing || _assignedCourses.isEmpty) return;

    _isAutoRefreshing = true;
    _lastRefreshTime = DateTime.now();

    try {
      // Refresh course stats first
      await _refreshCourseStats();

      // Then refresh video progress for all courses silently
      for (final course in _assignedCourses) {
        await _refreshCourseVideoProgress(course.id);
      }
    } catch (e) {
      debugPrint('Auto-refresh error: $e');
    } finally {
      _isAutoRefreshing = false;
    }
  }

  // Refresh course stats and video analytics (completion counts, progress percentages)
  Future<void> _refreshCourseStats() async {
    try {
      final response = await _apiService.getUserCourses();

      if (!mounted) return;

      if (response['success'] == true && response['courses'] != null) {
        final List<Course> updatedCourses = [];
        bool hasVideoAnalyticsChanges = false;

        for (var courseData in response['courses']) {
          // Create course from API data
          Course course = Course.fromJson(courseData);

          // Get detailed video stats for this course to calculate accurate stats
          try {
            final videoResponse = await _apiService.getCourseVideos(course.id);
            if (videoResponse['success'] == true && videoResponse['videos'] != null) {
              final videos = videoResponse['videos'] as List;

              // Calculate accurate stats from video data
              final totalVideos = videos.length;
              final completedVideos = videos.where((v) => v['is_completed'] == true).length;
              final unlockedVideos = videos.where((v) => v['is_unlocked'] == true).length;
              final progressPercentage = totalVideos > 0 ? ((completedVideos / totalVideos) * 100).round() : 0;

              // Check for video analytics changes (completion status, watch time, unlock status)
              if (_courseVideos.containsKey(course.id)) {
                final currentVideos = _courseVideos[course.id]!;
                for (var videoData in videos) {
                  final currentVideo = currentVideos.firstWhere(
                    (v) => v.id == videoData['id'],
                    orElse: () => CourseVideo(
                      id: 0, title: '', description: '', videoUrl: '',
                      weekNumber: 1, sequenceNumber: 1, isUnlocked: false, isCompleted: false
                    ),
                  );

                  // Check for analytics changes
                  if (currentVideo.id != 0 && (
                    currentVideo.isCompleted != (videoData['is_completed'] == true) ||
                    currentVideo.isUnlocked != (videoData['is_unlocked'] == true) ||
                    currentVideo.watchDurationSeconds != (videoData['watch_duration_seconds'] ?? 0) ||
                    currentVideo.lastPositionSeconds != (videoData['last_position_seconds'] ?? 0)
                  )) {
                    hasVideoAnalyticsChanges = true;
                    debugPrint('Video analytics changed for ${videoData['title']}: completed=${videoData['is_completed']}, unlocked=${videoData['is_unlocked']}, watch_time=${videoData['watch_duration_seconds']}s');
                  }
                }
              }

              // Update course videos with fresh analytics data
              final List<CourseVideo> courseVideos = [];
              for (var videoData in videos) {
                courseVideos.add(CourseVideo.fromJson(videoData));
              }
              _courseVideos[course.id] = courseVideos;
              _calculateWeekStatus(courseVideos);

              // Create updated course with accurate stats
              course = Course(
                id: course.id,
                title: course.title,
                description: course.description,
                thumbnailUrl: course.thumbnailUrl,
                durationWeeks: course.durationWeeks,
                startDate: course.startDate,
                endDate: course.endDate,
                status: course.status,
                totalVideos: totalVideos,
                unlockedVideos: unlockedVideos,
                completedVideos: completedVideos,
                progressPercentage: progressPercentage,
                progress: course.progress,
                imageUrl: course.imageUrl,
                instructor: course.instructor,
                level: course.level,
              );

              debugPrint('Updated course ${course.title}: ${totalVideos} total, ${completedVideos} completed, ${progressPercentage}% progress');
            }
          } catch (e) {
            debugPrint('Error fetching video stats for course ${course.id}: $e');
            // Keep original course data if video fetch fails
          }

          updatedCourses.add(course);
        }

        // Check if there are actual changes in course stats
        bool hasStatsChanges = false;
        if (updatedCourses.length == _assignedCourses.length) {
          for (int i = 0; i < updatedCourses.length; i++) {
            final current = _assignedCourses[i];
            final updated = updatedCourses[i];

            if (current.id == updated.id &&
                (current.completedVideos != updated.completedVideos ||
                 current.totalVideos != updated.totalVideos ||
                 current.progressPercentage != updated.progressPercentage ||
                 current.unlockedVideos != updated.unlockedVideos)) {
              hasStatsChanges = true;
              debugPrint('Stats changed for course ${current.title}: ${current.totalVideos}→${updated.totalVideos} videos, ${current.progressPercentage}→${updated.progressPercentage}% progress');
              break;
            }
          }
        } else {
          hasStatsChanges = true; // Course count changed
          debugPrint('Course count changed: ${_assignedCourses.length} → ${updatedCourses.length}');
        }

        // Update UI if there are stats or analytics changes
        if (hasStatsChanges || hasVideoAnalyticsChanges) {
          setState(() {
            _assignedCourses = updatedCourses;

            // Update last opened course if it exists in the updated list
            if (_lastOpenedCourse != null) {
              final updatedLastCourse = updatedCourses.firstWhere(
                (course) => course.id == _lastOpenedCourse!.id,
                orElse: () => _lastOpenedCourse!,
              );
              _lastOpenedCourse = updatedLastCourse;
              debugPrint('Updated last opened course: ${_lastOpenedCourse!.title} - ${_lastOpenedCourse!.totalVideos} videos, ${_lastOpenedCourse!.progressPercentage}% progress');
            }
          });

          if (hasVideoAnalyticsChanges) {
            debugPrint('Video analytics synchronized from dashboard - UI updated');
          }
          if (hasStatsChanges) {
            debugPrint('Course stats refreshed successfully - UI updated');
          }
        } else {
          debugPrint('No course stats or video analytics changes detected');
        }
      }
    } catch (e) {
      debugPrint('Error refreshing course stats and video analytics: $e');
    }
  }

  // Refresh video progress for a specific course
  Future<void> _refreshCourseVideoProgress(int courseId) async {
    try {
      final data = await _apiService.getCourseVideos(courseId);

      if (!mounted) return;

      if (data['success'] == true && data['videos'] != null) {
        final List<CourseVideo> updatedVideos = [];
        for (var videoData in data['videos']) {
          updatedVideos.add(CourseVideo.fromJson(videoData));
        }

        // Update only if there are actual changes
        if (_courseVideos.containsKey(courseId)) {
          final currentVideos = _courseVideos[courseId]!;
          bool hasChanges = false;

          for (int i = 0; i < updatedVideos.length && i < currentVideos.length; i++) {
            final current = currentVideos[i];
            final updated = updatedVideos[i];

            if (current.watchDurationSeconds != updated.watchDurationSeconds ||
                current.lastPositionSeconds != updated.lastPositionSeconds ||
                current.isCompleted != updated.isCompleted) {
              hasChanges = true;
              break;
            }
          }

          if (hasChanges) {
            setState(() {
              _courseVideos[courseId] = updatedVideos;
              _calculateWeekStatus(updatedVideos);
            });
          }
        }
      }
    } catch (e) {
      // Silently handle errors during auto-refresh
      debugPrint('Error refreshing course $courseId progress: $e');
    }
  }

  Future<void> _loadLastOpenedCourse() async {
    try {
      // Find the last opened course from the assigned courses
      final lastCourse = _assignedCourses.isNotEmpty
          ? await _trackingService.findLastOpenedCourse(_assignedCourses)
          : null;

      // Get the last accessed video ID
      final lastVideoId = await _trackingService.getLastAccessedVideoId();
      CourseVideo? lastVideo;

      // Find the video in the course videos
      if (lastVideoId != null && lastCourse != null && _courseVideos.containsKey(lastCourse.id)) {
        final videos = _courseVideos[lastCourse.id] ?? [];
        for (var video in videos) {
          if (video.id == lastVideoId) {
            lastVideo = video;
            break;
          }
        }
      }

      if (mounted) {
        setState(() {
          _lastOpenedCourse = lastCourse;
          _lastAccessedVideo = lastVideo;

          // If we have a last opened course, expand it by default
          if (lastCourse != null) {
            _expandedCourses[lastCourse.id] = true;
          }
        });
      }
    } catch (e) {
      print('Error loading last opened course: $e');
    }
  }

  Future<void> _loadAssignedCourses({bool forceRefresh = false}) async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // Load assigned courses
      final response = await _apiService.getUserCourses();

      if (!mounted) return;

      if (response['success'] == true && response['courses'] != null) {
        final List<Course> courses = [];

        for (var courseData in response['courses']) {
          // Create course from API data
          Course course = Course.fromJson(courseData);

          // Get detailed video stats for accurate course information
          try {
            final videoResponse = await _apiService.getCourseVideos(course.id);
            if (videoResponse['success'] == true && videoResponse['videos'] != null) {
              final videos = videoResponse['videos'] as List;

              // Calculate accurate stats from video data
              final totalVideos = videos.length;
              final completedVideos = videos.where((v) => v['is_completed'] == true).length;
              final unlockedVideos = videos.where((v) => v['is_unlocked'] == true).length;
              final progressPercentage = totalVideos > 0 ? ((completedVideos / totalVideos) * 100).round() : 0;

              // Create updated course with accurate stats
              course = Course(
                id: course.id,
                title: course.title,
                description: course.description,
                thumbnailUrl: course.thumbnailUrl,
                durationWeeks: course.durationWeeks,
                startDate: course.startDate,
                endDate: course.endDate,
                status: course.status,
                totalVideos: totalVideos,
                unlockedVideos: unlockedVideos,
                completedVideos: completedVideos,
                progressPercentage: progressPercentage,
                progress: course.progress,
                imageUrl: course.imageUrl,
                instructor: course.instructor,
                level: course.level,
              );

              debugPrint('Loaded course ${course.title}: ${totalVideos} total videos, ${completedVideos} completed, ${progressPercentage}% progress');

              // Also load and store the video data
              final List<CourseVideo> courseVideos = [];
              for (var videoData in videos) {
                courseVideos.add(CourseVideo.fromJson(videoData));
              }
              _courseVideos[course.id] = courseVideos;
              _calculateWeekStatus(courseVideos);
            }
          } catch (e) {
            debugPrint('Error loading video stats for course ${course.id}: $e');
            // Keep original course data if video fetch fails
          }

          courses.add(course);
        }

        setState(() {
          _assignedCourses = courses;
          _isLoading = false;

          // Initialize expanded state for each course
          for (final course in courses) {
            _expandedCourses[course.id] = false;
          }

          // If no course is selected yet and we have courses, select the first one
          if (_selectedCourse == null && courses.isNotEmpty) {
            _selectedCourse = courses[0]; // Use index instead of .first
            _saveSelectedCourse(courses[0]);
          }
        });

        // Load last opened course after courses are loaded
        await _loadLastOpenedCourse();

        debugPrint('Successfully loaded ${courses.length} courses with accurate stats');
      } else {
        setState(() {
          _assignedCourses = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _errorMessage = e.toString().replaceAll('Exception: ', '');
        _isLoading = false;
      });
    }
  }

  Future<void> _loadCourseVideos(int courseId, {bool forceRefresh = false}) async {
    try {
      final data = await _apiService.getCourseVideos(courseId);

      if (!mounted) return;

      if (data['success'] == true && data['videos'] != null) {
        final List<CourseVideo> videos = [];
        for (var videoData in data['videos']) {
          videos.add(CourseVideo.fromJson(videoData));
        }

        setState(() {
          _courseVideos[courseId] = videos;
          // Calculate week status for this course only
          if (_courseVideos.containsKey(courseId)) {
            _calculateWeekStatus(_courseVideos[courseId]!);
          }
        });
      } else {
        setState(() {
          _courseVideos[courseId] = [];
        });
      }
    } catch (e) {
      // Silently handle error, as we already have the course info
      print('Error loading videos for course $courseId: $e');
      if (mounted) {
        setState(() {
          _courseVideos[courseId] = [];
        });
      }
    }
  }

  // Update a single video without reloading the entire course
  Future<void> _updateSingleVideo(CourseVideo updatedVideo, int courseId) async {
    if (!mounted || !_courseVideos.containsKey(courseId)) return;

    final videos = _courseVideos[courseId]!;
    final videoIndex = videos.indexWhere((v) => v.id == updatedVideo.id);

    if (videoIndex != -1) {
      setState(() {
        // Replace the video with the updated one
        videos[videoIndex] = updatedVideo;
        _courseVideos[courseId] = videos;

        // If this is the last accessed video, update it
        if (_lastAccessedVideo != null && _lastAccessedVideo!.id == updatedVideo.id) {
          _lastAccessedVideo = updatedVideo;
        }

        // Recalculate week status only for the affected week
        _calculateWeekStatusForWeek(updatedVideo.weekNumber, videos);
      });
    }
  }

  // Calculate week status for a specific week only
  void _calculateWeekStatusForWeek(int weekNumber, List<CourseVideo> videos) {
    // Filter videos for this week only
    final weekVideos = videos.where((v) => v.weekNumber == weekNumber).toList();
    if (weekVideos.isEmpty) return;

    final allCompleted = weekVideos.every((v) => v.isCompleted);
    final anyUnlocked = weekVideos.any((v) => v.isUnlocked);

    // Determine if this is the current week
    bool isCurrent = false;
    if (anyUnlocked && !allCompleted) {
      // Check if this is the earliest week with unlocked but not all completed videos
      final earlierWeeks = videos
          .where((v) => v.weekNumber < weekNumber)
          .toList();

      final hasEarlierIncomplete = earlierWeeks
          .any((v) => v.isUnlocked && !v.isCompleted);

      isCurrent = !hasEarlierIncomplete;
    }

    // Set status for this week
    if (allCompleted) {
      _weekStatus[weekNumber] = 'completed';
    } else if (isCurrent) {
      _weekStatus[weekNumber] = 'current';
    } else if (anyUnlocked) {
      _weekStatus[weekNumber] = 'active';
    } else {
      _weekStatus[weekNumber] = 'locked';
    }
  }





  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () => _loadAssignedCourses(forceRefresh: true),
          child: CustomScrollView(
            physics: const AlwaysScrollableScrollPhysics(parent: BouncingScrollPhysics()),
            slivers: [
            // Modern Premium header with enhanced visual effects
            SliverPremiumHeader(
              title: 'My Workouts',
              subtitle: _lastOpenedCourse != null
                  ? 'Continue your fitness journey'
                  : 'Start your fitness journey today',
              actions: [
                // Theme Toggle Button
                const ThemeToggleButton(iconOnly: true),
                // Refresh button with improved visual feedback
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.refresh_rounded, color: Colors.white),
                    tooltip: 'Refresh workouts',
                    onPressed: () {
                      // Show a subtle loading indicator
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Refreshing workouts...'),
                          duration: Duration(seconds: 1),
                          behavior: SnackBarBehavior.floating,
                        ),
                      );
                      _loadAssignedCourses(forceRefresh: true);
                    },
                  ),
                ),
              ],

              // Enhanced decoration with more subtle gradient
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: isDarkMode
                      ? [
                          const Color(0xFF1A1A1A),
                          const Color(0xFF2C2C2C),
                        ]
                      : [
                          theme.colorScheme.primary,
                          Color.lerp(theme.colorScheme.primary, theme.colorScheme.primaryContainer, 0.5) ?? theme.colorScheme.primary,
                        ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
            ),

            // Loading state with improved visual feedback
            _isLoading
                ? SliverFillRemaining(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 40,
                            height: 40,
                            child: CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                              strokeWidth: 3,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Loading your workouts...',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onBackground.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                : _errorMessage.isNotEmpty
                    ? SliverFillRemaining(
                        child: _buildErrorState(),
                      )
                    : _buildWorkoutContent(),

            // Bottom padding with subtle fade effect
            SliverToBoxAdapter(
              child: Container(
                height: 100,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      theme.colorScheme.background.withOpacity(0),
                      theme.colorScheme.background,
                    ],
                    stops: const [0.0, 0.5],
                  ),
                ),
              ),
            ),
          ],
          ),
        ),
      ),
    );
  }

  Widget _buildActiveWorkoutBanner() {
    if (_lastOpenedCourse == null) return const SizedBox.shrink();

    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate available height and adjust layout accordingly
        final availableHeight = constraints.maxHeight;
        final isCompact = availableHeight < 80; // Compact mode for tight spaces

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          constraints: BoxConstraints(
            maxHeight: availableHeight > 0 ? availableHeight : double.infinity,
          ),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withOpacity(0.15),
                Colors.white.withOpacity(0.08),
              ],
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.white.withOpacity(0.1),
              width: 0.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
              child: InkWell(
                borderRadius: BorderRadius.circular(16),
                onTap: () {
                  if (_lastOpenedCourse != null) {
                    _selectCourse(_lastOpenedCourse!);
                  }
                },
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: isCompact ? 4 : 6, // Even more compact for tight spaces
                  ),
                  child: IntrinsicHeight( // Use intrinsic height for better layout
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Main content row
                        Flexible(
                          child: Row(
                            children: [
                              // Modern play icon with gradient background
                              Container(
                                padding: EdgeInsets.all(isCompact ? 6 : 8), // Smaller in compact mode
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      Colors.white.withOpacity(0.3),
                                      Colors.white.withOpacity(0.1),
                                    ],
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Icon(
                                  Icons.play_circle_fill_rounded,
                                  color: Colors.white,
                                  size: isCompact ? 16 : 20, // Smaller in compact mode
                                ),
                              ),
                              SizedBox(width: isCompact ? 8 : 12),

                              // Course info
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    // Top row with status and video count
                                    Row(
                                      children: [
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                            horizontal: isCompact ? 4 : 6,
                                            vertical: isCompact ? 0 : 1,
                                          ),
                                          decoration: BoxDecoration(
                                            color: Colors.white.withOpacity(0.2),
                                            borderRadius: BorderRadius.circular(4),
                                          ),
                                          child: Text(
                                            'ACTIVE',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: isCompact ? 8 : 9,
                                              fontWeight: FontWeight.w600,
                                              letterSpacing: 0.5,
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: isCompact ? 4 : 6),
                                        Flexible(
                                          child: Text(
                                            '${_lastOpenedCourse?.completedVideos ?? 0}/${_lastOpenedCourse?.totalVideos ?? 0} videos',
                                            style: TextStyle(
                                              color: Colors.white70,
                                              fontSize: isCompact ? 9 : 10,
                                              fontWeight: FontWeight.w500,
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ],
                                    ),
                                    if (!isCompact) SizedBox(height: 4),
                                    // Course title
                                    Text(
                                      _lastOpenedCourse?.title ?? '',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: isCompact ? 13 : 15,
                                        fontWeight: FontWeight.bold,
                                        letterSpacing: -0.3,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
  Widget _buildErrorState() {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Modern error illustration
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: isDarkMode
                    ? Colors.red.shade900.withOpacity(0.2)
                    : Colors.red.shade50,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Icon(
                  Icons.error_outline_rounded,
                  size: 64,
                  color: isDarkMode ? Colors.red.shade300 : Colors.red.shade400,
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Error title
            Text(
              'Something went wrong',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.error,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),

            // Error message
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? theme.colorScheme.error.withOpacity(0.1)
                    : theme.colorScheme.error.withOpacity(0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.colorScheme.error.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Text(
                _errorMessage,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: isDarkMode
                      ? theme.colorScheme.onError.withOpacity(0.8)
                      : theme.colorScheme.error.withOpacity(0.8),
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 32),

            // Retry button with improved design
            SizedBox(
              width: 200,
              child: ElevatedButton.icon(
                onPressed: () => _loadAssignedCourses(forceRefresh: true),
                icon: const Icon(Icons.refresh_rounded),
                label: const Text('Try Again'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
              ),
            ),

            // Help text
            const SizedBox(height: 16),
            TextButton.icon(
              onPressed: () {
                // Show help dialog
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Need Help?'),
                    content: const Text(
                      'If this issue persists, please try the following:\n\n'
                      '• Check your internet connection\n'
                      '• Restart the app\n'
                      '• Contact support if the problem continues',
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('OK'),
                      ),
                    ],
                  ),
                );
              },
              icon: Icon(
                Icons.help_outline,
                size: 16,
                color: theme.colorScheme.primary.withOpacity(0.8),
              ),
              label: Text(
                'Need help?',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.primary.withOpacity(0.8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWorkoutContent() {
    if (_assignedCourses.isEmpty) {
      return SliverFillRemaining(
        child: _buildEmptyState(),
      );
    }

    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      sliver: SliverList(
        delegate: SliverChildListDelegate([
          // Course selector with horizontal scrolling
          _buildCourseSelector(),

          // Selected course details and progress
          if (_selectedCourse != null) _buildSelectedCourseDetails(),

          // Selected course videos
          if (_selectedCourse != null) _buildCourseVideosSection(_selectedCourse!),
        ]),
      ),
    );
  }

  Widget _buildCourseSelector() {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title with icon
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 16, top: 8),
          child: Row(
            children: [
              Icon(
                Icons.fitness_center_rounded,
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'My Courses',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
        ),

        // Horizontal scrollable course cards
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            physics: const BouncingScrollPhysics(),
            itemCount: _assignedCourses.length,
            itemBuilder: (context, index) {
              final course = _assignedCourses[index];
              final isSelected = _selectedCourse?.id == course.id;
              final progressPercentage = course.progressPercentage;

              return GestureDetector(
                onTap: () => _selectCourse(course),
                child: Container(
                  width: 200,
                  margin: const EdgeInsets.only(right: 12, bottom: 4),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: isSelected
                          ? [
                              theme.colorScheme.primary,
                              Color.lerp(theme.colorScheme.primary, theme.colorScheme.primaryContainer, 0.7) ?? theme.colorScheme.primary,
                            ]
                          : isDarkMode
                              ? [
                                  const Color(0xFF2A2A2A),
                                  const Color(0xFF1E1E1E),
                                ]
                              : [
                                  Colors.white,
                                  const Color(0xFFF8F8F8),
                                ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: isSelected
                          ? theme.colorScheme.primary
                          : isDarkMode
                              ? Colors.grey.shade800
                              : Colors.grey.shade200,
                      width: isSelected ? 2 : 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: isSelected
                            ? theme.colorScheme.primary.withOpacity(0.3)
                            : Colors.black.withOpacity(0.05),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Stack(
                    children: [
                      // Course content
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Course icon and title
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? Colors.white.withOpacity(0.2)
                                        : theme.colorScheme.primary.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Icon(
                                    Icons.fitness_center_rounded,
                                    color: isSelected
                                        ? Colors.white
                                        : theme.colorScheme.primary,
                                    size: 16,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    course.title,
                                    style: theme.textTheme.titleSmall?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: isSelected
                                          ? Colors.white
                                          : theme.colorScheme.onBackground,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),

                            const Spacer(),

                            // Course progress
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  '${course.completedVideos}/${course.totalVideos} videos',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: isSelected
                                        ? Colors.white.withOpacity(0.8)
                                        : theme.colorScheme.onBackground.withOpacity(0.6),
                                  ),
                                ),
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? Colors.white.withOpacity(0.2)
                                        : theme.colorScheme.primary.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Text(
                                    '$progressPercentage%',
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: isSelected
                                          ? Colors.white
                                          : theme.colorScheme.primary,
                                    ),
                                  ),
                                ),
                              ],
                            ),

                            const SizedBox(height: 8),

                            // Progress bar
                            ClipRRect(
                              borderRadius: BorderRadius.circular(4),
                              child: LinearProgressIndicator(
                                value: progressPercentage / 100,
                                minHeight: 4,
                                backgroundColor: isSelected
                                    ? Colors.white.withOpacity(0.2)
                                    : theme.colorScheme.primary.withOpacity(0.1),
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  isSelected
                                      ? Colors.white
                                      : theme.colorScheme.primary,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Selected indicator
                      if (isSelected)
                        Positioned(
                          top: 8,
                          right: 8,
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Icon(
                              Icons.check,
                              color: theme.colorScheme.primary,
                              size: 12,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSelectedCourseDetails() {
    if (_selectedCourse == null) return const SizedBox.shrink();

    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final progressPercentage = _selectedCourse!.progressPercentage;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Course title and description
          Text(
            _selectedCourse!.title,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onBackground,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _selectedCourse!.description,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onBackground.withOpacity(0.7),
              height: 1.4,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 20),

          // Course stats
          Row(
            children: [
              // Videos stat
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? Colors.black.withOpacity(0.2)
                        : theme.colorScheme.primary.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.videocam_rounded,
                            size: 16,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'Videos',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onBackground.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${_selectedCourse!.completedVideos}/${_selectedCourse!.totalVideos}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onBackground,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 12),

              // Progress stat
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? Colors.black.withOpacity(0.2)
                        : theme.colorScheme.primary.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.trending_up_rounded,
                            size: 16,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'Progress',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onBackground.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '$progressPercentage%',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: progressPercentage >= 100
                              ? Colors.green
                              : theme.colorScheme.onBackground,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 12),

              // Duration stat (placeholder - would be calculated from actual videos)
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? Colors.black.withOpacity(0.2)
                        : theme.colorScheme.primary.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.timer_outlined,
                            size: 16,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'Duration',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onBackground.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${_selectedCourse!.totalVideos * 10} min', // Placeholder calculation
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onBackground,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Progress bar with label
          Row(
            children: [
              Text(
                'Overall Progress',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onBackground,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: progressPercentage >= 100
                      ? Colors.green.withOpacity(0.1)
                      : theme.colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  progressPercentage >= 100 ? 'Completed' : 'In Progress',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: progressPercentage >= 100
                        ? Colors.green
                        : theme.colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: LinearProgressIndicator(
              value: progressPercentage / 100,
              minHeight: 8,
              backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
              valueColor: AlwaysStoppedAnimation<Color>(
                progressPercentage >= 100 ? Colors.green : theme.colorScheme.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showCourseSelectionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Course'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _assignedCourses.length,
            itemBuilder: (context, index) {
              final course = _assignedCourses[index];
              final isSelected = _selectedCourse?.id == course.id;

              return ListTile(
                title: Text(
                  course.title,
                  style: TextStyle(
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                subtitle: Text(
                  '${course.progressPercentage}% completed',
                  style: TextStyle(
                    fontSize: 12,
                  ),
                ),
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.fitness_center,
                    color: isSelected
                      ? Colors.white
                      : Theme.of(context).colorScheme.primary,
                    size: 20,
                  ),
                ),
                trailing: isSelected
                  ? Icon(
                      Icons.check_circle,
                      color: Theme.of(context).colorScheme.primary,
                    )
                  : null,
                onTap: () {
                  Navigator.of(context).pop();
                  _selectCourse(course);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _selectCourse(Course course) {
    // Only update if the selected course has changed
    if (_selectedCourse == null || _selectedCourse!.id != course.id) {
      setState(() {
        _selectedCourse = course;
      });

      // Save the selected course without triggering a full reload
      _saveSelectedCourseOptimized(course);

      // Show confirmation
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Selected course: ${course.title}'),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  // Optimized version that doesn't trigger unnecessary reloads
  Future<void> _saveSelectedCourseOptimized(Course course) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_selectedCourseIdKey, course.id);

      // Update last opened course without triggering a full reload
      if (_lastOpenedCourse == null || _lastOpenedCourse!.id != course.id) {
        await _trackingService.saveLastOpenedCourse(course);

        if (mounted) {
          setState(() {
            _lastOpenedCourse = course;
            _expandedCourses[course.id] = true;
          });
        }
      }
    } catch (e) {
      print('Error saving selected course: $e');
    }
  }



  // Helper method to determine week status
  void _calculateWeekStatus(List<CourseVideo> videos) {
    // Group videos by week
    final Map<int, List<CourseVideo>> videosByWeek = {};
    for (final video in videos) {
      if (!videosByWeek.containsKey(video.weekNumber)) {
        videosByWeek[video.weekNumber] = [];
      }
      videosByWeek[video.weekNumber]!.add(video);
    }

    // Determine status for each week
    _weekStatus.clear();

    // Find current week (the earliest week with unlocked but not all completed videos)
    int? currentWeek;
    for (final weekNumber in videosByWeek.keys.toList()..sort()) {
      final weekVideos = videosByWeek[weekNumber]!;
      final allCompleted = weekVideos.every((v) => v.isCompleted);
      final anyUnlocked = weekVideos.any((v) => v.isUnlocked);

      if (anyUnlocked && !allCompleted) {
        currentWeek = weekNumber;
        break;
      }
    }

    // If no current week found, use the earliest week with any unlocked videos
    if (currentWeek == null) {
      for (final weekNumber in videosByWeek.keys.toList()..sort()) {
        final weekVideos = videosByWeek[weekNumber]!;
        final anyUnlocked = weekVideos.any((v) => v.isUnlocked);

        if (anyUnlocked) {
          currentWeek = weekNumber;
          break;
        }
      }
    }

    // Set status for each week
    for (final weekNumber in videosByWeek.keys) {
      final weekVideos = videosByWeek[weekNumber]!;
      final allCompleted = weekVideos.every((v) => v.isCompleted);
      final anyUnlocked = weekVideos.any((v) => v.isUnlocked);

      if (allCompleted) {
        _weekStatus[weekNumber] = 'completed';
      } else if (weekNumber == currentWeek) {
        _weekStatus[weekNumber] = 'current';
      } else if (anyUnlocked) {
        _weekStatus[weekNumber] = 'active';
      } else {
        _weekStatus[weekNumber] = 'locked';
      }
    }
  }

  Widget _buildCourseVideosSection(Course course) {
    final theme = Theme.of(context);

    // If videos are not loaded yet, show loading indicator with improved design
    if (!_courseVideos.containsKey(course.id)) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 40,
                height: 40,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                  strokeWidth: 3,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Loading videos...',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onBackground.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
      );
    }

    final videos = _courseVideos[course.id] ?? [];

    if (videos.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(32.0),
        decoration: BoxDecoration(
          color: theme.brightness == Brightness.dark
              ? Colors.black.withOpacity(0.2)
              : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.videocam_off_outlined,
                size: 48,
                color: theme.colorScheme.primary.withOpacity(0.5),
              ),
              const SizedBox(height: 16),
              Text(
                'No videos available for this course',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.onBackground.withOpacity(0.7),
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'Check back later or contact your trainer',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onBackground.withOpacity(0.5),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // Process videos for display
    List<CourseVideo> processedVideos = _processVideosForDisplay(videos);

    // Calculate week status
    _calculateWeekStatus(processedVideos);

    // Sort videos by week first, then by sequence number
    processedVideos.sort((a, b) {
      int weekComparison = a.weekNumber.compareTo(b.weekNumber);
      if (weekComparison != 0) return weekComparison;
      return a.sequenceNumber.compareTo(b.sequenceNumber);
    });

    // Filter videos based on selected filter
    if (_selectedFilter == 'Unlocked') {
      processedVideos = processedVideos.where((video) => video.isUnlocked).toList();
    } else if (_selectedFilter == 'Completed') {
      processedVideos = processedVideos.where((video) => video.isCompleted).toList();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Filter and sort options
        _buildFilterOptions(),

        // Video count summary
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 12),
          child: Text(
            '${processedVideos.length} videos${_selectedFilter != 'All' ? ' • $_selectedFilter' : ''}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onBackground.withOpacity(0.6),
              fontWeight: FontWeight.w500,
            ),
          ),
        ),

        // Videos list
        _buildVideosList(processedVideos),
      ],
    );
  }

  Widget _buildFilterOptions() {
    final theme = Theme.of(context);

    return Container(
      height: 40,
      margin: const EdgeInsets.only(top: 8, bottom: 8),
      child: Row(
        children: [
          // Filter options only (removed sort dropdown)
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              physics: const BouncingScrollPhysics(),
              child: Row(
                children: [
                  _buildFilterChip('All'),
                  const SizedBox(width: 8),
                  _buildFilterChip('Unlocked'),
                  const SizedBox(width: 8),
                  _buildFilterChip('Completed'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String filter) {
    final theme = Theme.of(context);
    final isSelected = _selectedFilter == filter;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedFilter = filter;
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? theme.colorScheme.primary
              : theme.brightness == Brightness.dark
                  ? Colors.grey.shade800.withOpacity(0.3)
                  : Colors.grey.shade200,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          filter,
          style: theme.textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
            color: isSelected
                ? theme.colorScheme.onPrimary
                : theme.colorScheme.onBackground.withOpacity(0.7),
          ),
        ),
      ),
    );
  }

  Widget _buildVideosList(List<CourseVideo> videos) {
    final theme = Theme.of(context);

    // Group videos by week
    final Map<int, List<CourseVideo>> videosByWeek = {};
    for (final video in videos) {
      if (!videosByWeek.containsKey(video.weekNumber)) {
        videosByWeek[video.weekNumber] = [];
      }
      videosByWeek[video.weekNumber]!.add(video);
    }

    // Sort weeks
    final sortedWeeks = videosByWeek.keys.toList()..sort();

    return Column(
      children: [
        for (final weekNumber in sortedWeeks) ...[
          // Week header
          _buildWeekHeader(weekNumber, videosByWeek[weekNumber]!.length, theme),

          // Videos for this week
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: videosByWeek[weekNumber]!.length,
            itemBuilder: (context, index) {
              final video = videosByWeek[weekNumber]![index];
              return _buildVideoItem(video);
            },
          ),

          // Add spacing between weeks
          const SizedBox(height: 16),
        ],
      ],
    );
  }

  // Build a header for each week showing its status
  Widget _buildWeekHeader(int weekNumber, int videoCount, ThemeData theme) {
    final weekStatus = _weekStatus[weekNumber] ?? 'locked';

    // Define styling based on week status
    Color textColor;
    String statusText;
    IconData statusIcon;

    switch (weekStatus) {
      case 'completed':
        textColor = Colors.green;
        statusText = 'Completed';
        statusIcon = Icons.check_circle;
        break;
      case 'current':
        textColor = theme.colorScheme.primary;
        statusText = 'Current Week';
        statusIcon = Icons.play_circle_filled;
        break;
      case 'active':
        textColor = theme.colorScheme.primary;
        statusText = 'In Progress';
        statusIcon = Icons.access_time;
        break;
      case 'locked':
      default:
        textColor = Colors.orange;
        statusText = 'Locked';
        statusIcon = Icons.lock_outline;
        break;
    }

    return Padding(
      padding: const EdgeInsets.only(left: 4, right: 4, top: 16, bottom: 8),
      child: Row(
        children: [
          // Week number
          Text(
            'Week $weekNumber',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onBackground,
            ),
          ),
          const SizedBox(width: 8),

          // Video count
          Text(
            '• $videoCount ${videoCount == 1 ? 'video' : 'videos'}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onBackground.withOpacity(0.6),
            ),
          ),

          const Spacer(),

          // Week status
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                statusIcon,
                size: 16,
                color: textColor,
              ),
              const SizedBox(width: 4),
              Text(
                statusText,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: textColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Modern empty state illustration
            Container(
              width: 160,
              height: 160,
              decoration: BoxDecoration(
                color: isDarkMode
                    ? theme.colorScheme.primary.withOpacity(0.1)
                    : theme.colorScheme.primary.withOpacity(0.05),
                shape: BoxShape.circle,
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Background decorative elements
                  Positioned(
                    top: 30,
                    left: 30,
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withOpacity(0.2),
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 40,
                    right: 35,
                    child: Container(
                      width: 15,
                      height: 15,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withOpacity(0.15),
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),

                  // Main icon
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? theme.colorScheme.primary.withOpacity(0.2)
                          : theme.colorScheme.primary.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.fitness_center_rounded,
                      size: 60,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),

            // Empty state title
            Text(
              'No Workout Courses Yet',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onBackground,
                letterSpacing: -0.5,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // Empty state description
            Text(
              'Your personalized workout courses will appear here once they are assigned by your trainer.',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onBackground.withOpacity(0.7),
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Check back later or contact your trainer to get started.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onBackground.withOpacity(0.6),
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Contact trainer button
                OutlinedButton.icon(
                  onPressed: () {
                    // Show contact options dialog
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: const Text('Contact Your Trainer'),
                        content: const Text(
                          'Would you like to send a message to your trainer about getting started with workout courses?',
                        ),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: const Text('Cancel'),
                          ),
                          TextButton(
                            onPressed: () {
                              Navigator.pop(context);
                              // Show a confirmation message
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Message sent to your trainer'),
                                  behavior: SnackBarBehavior.floating,
                                ),
                              );
                            },
                            child: const Text('Send Message'),
                          ),
                        ],
                      ),
                    );
                  },
                  icon: const Icon(Icons.message_outlined, size: 18),
                  label: const Text('Contact Trainer'),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: theme.colorScheme.primary),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),

                // Refresh button
                ElevatedButton.icon(
                  onPressed: () {
                    // Show a subtle loading indicator
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Checking for new workouts...'),
                        duration: Duration(seconds: 1),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                    _loadAssignedCourses();
                  },
                  icon: const Icon(Icons.refresh_rounded, size: 18),
                  label: const Text('Refresh'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCourseCard(Course course) {
    final theme = Theme.of(context);
    _expandedCourses[course.id] = true;

    return PremiumCard(
      margin: const EdgeInsets.only(bottom: 24),
      padding: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Course header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  KFTDesignSystem.primaryColor.withOpacity(0.05),
                  KFTDesignSystem.primaryColor.withOpacity(0.1),
                ],
              ),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(20),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.fitness_center_rounded,
                        color: theme.colorScheme.primary,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            course.title,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'By KFT Fitness',
                            style: TextStyle(
                              fontSize: 14,
                              color: theme.colorScheme.onSurface.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: KFTDesignSystem.primaryColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${course.progressPercentage}%',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  course.description,
                  style: TextStyle(
                    fontSize: 14,
                    color: KFTDesignSystem.textSecondaryColor,
                    height: 1.4,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 16),

                // Progress bar
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Course Progress',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: KFTDesignSystem.textSecondaryColor,
                          ),
                        ),
                        Text(
                          '${course.completedVideos}/${course.totalVideos} videos',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: KFTDesignSystem.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: LinearProgressIndicator(
                        value: course.progressPercentage / 100,
                        minHeight: 8,
                        backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                        valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Videos list
          if (_courseVideos.containsKey(course.id))
            _buildCourseVideosSection(course),
        ],
      ),
    );
  }

  Widget _buildWeekSection(int weekNumber, List<CourseVideo> videos) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // Check if any videos in this week are unlocked
    final hasUnlockedVideos = videos.any((video) => video.isUnlocked);

    // Count completed videos in this week
    final completedVideos = videos.where((video) => video.isCompleted).length;
    final totalVideos = videos.length;
    final weekProgress = totalVideos > 0 ? (completedVideos / totalVideos) * 100 : 0.0;

    // Get the next unlock date for this week (if any videos are locked)
    String? nextUnlockDate;
    if (!hasUnlockedVideos) {
      // Find the earliest unlock date among locked videos
      for (final video in videos) {
        if (!video.isUnlocked && video.unlockDate != null) {
          if (nextUnlockDate == null || video.unlockDate!.compareTo(nextUnlockDate) < 0) {
            nextUnlockDate = video.unlockDate;
          }
        }
      }
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      clipBehavior: Clip.antiAlias,
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
          colorScheme: Theme.of(context).colorScheme.copyWith(
            primary: Theme.of(context).colorScheme.primary,
          ),
        ),
        child: ExpansionTile(
          initiallyExpanded: true,
          maintainState: true,
          tilePadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          childrenPadding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Week header with status
              Row(
                children: [
                  // Week icon with status-based styling
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: hasUnlockedVideos
                            ? [
                                theme.colorScheme.primary.withOpacity(0.2),
                                theme.colorScheme.primary.withOpacity(0.1),
                              ]
                            : [
                                Colors.orange.withOpacity(0.2),
                                Colors.orange.withOpacity(0.1),
                              ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      hasUnlockedVideos
                          ? weekProgress >= 100
                              ? Icons.check_circle_outline_rounded
                              : Icons.calendar_today_rounded
                          : Icons.lock_clock_rounded,
                      color: hasUnlockedVideos
                          ? weekProgress >= 100
                              ? Colors.green
                              : theme.colorScheme.primary
                          : Colors.orange,
                      size: 18,
                    ),
                  ),
                  const SizedBox(width: 14),

                  // Week title and subtitle
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Week $weekNumber',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onBackground,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          hasUnlockedVideos
                              ? '$completedVideos of $totalVideos videos completed'
                              : nextUnlockDate != null
                                  ? 'Unlocks on ${_formatDate(nextUnlockDate)}'
                                  : 'Locked content',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: hasUnlockedVideos
                                ? theme.colorScheme.onBackground.withOpacity(0.6)
                                : Colors.orange[700],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Status badge
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: hasUnlockedVideos
                            ? weekProgress >= 100
                                ? [
                                    Colors.green.withOpacity(0.2),
                                    Colors.green.withOpacity(0.1),
                                  ]
                                : [
                                    theme.colorScheme.primary.withOpacity(0.2),
                                    theme.colorScheme.primary.withOpacity(0.1),
                                  ]
                            : [
                                Colors.orange.withOpacity(0.2),
                                Colors.orange.withOpacity(0.1),
                              ],
                      ),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          hasUnlockedVideos
                              ? weekProgress >= 100
                                  ? Icons.check_circle_rounded
                                  : Icons.play_circle_outline_rounded
                              : Icons.lock_outline_rounded,
                          size: 14,
                          color: hasUnlockedVideos
                              ? weekProgress >= 100
                                  ? Colors.green
                                  : theme.colorScheme.primary
                              : Colors.orange[700],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          hasUnlockedVideos
                              ? weekProgress >= 100
                                  ? 'Completed'
                                  : 'In Progress'
                              : 'Locked',
                          style: theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: hasUnlockedVideos
                                ? weekProgress >= 100
                                    ? Colors.green
                                    : theme.colorScheme.primary
                                : Colors.orange[700],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              // Progress bar (only for unlocked weeks)
              if (hasUnlockedVideos) ...[
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(4),
                        child: LinearProgressIndicator(
                          value: weekProgress / 100,
                          minHeight: 4,
                          backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                          valueColor: AlwaysStoppedAnimation<Color>(
                            weekProgress >= 100 ? Colors.green : theme.colorScheme.primary,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      '${weekProgress.toInt()}%',
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: weekProgress >= 100 ? Colors.green : theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
          children: [
            // Subtle divider
            Divider(
              height: 1,
              thickness: 1,
              color: isDarkMode ? Colors.grey.shade800.withOpacity(0.5) : Colors.grey.shade200,
            ),

            // Video list with animated transitions
            AnimatedSize(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: Column(
                children: videos.map((video) => _buildVideoItem(video)).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoItem(CourseVideo video) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final isLocked = !video.isUnlocked;
    final isCompleted = video.isCompleted;
    final isLastAccessed = _lastAccessedVideo != null && _lastAccessedVideo!.id == video.id;

    // Determine status color
    final Color statusColor = isLocked
        ? Colors.orange
        : isCompleted
            ? Colors.green
            : theme.colorScheme.primary;

    // Determine week label
    final String weekLabel = 'Week ${video.weekNumber}';

    // Get week status for styling
    final weekStatus = _weekStatus[video.weekNumber] ?? 'locked';

    // Define container styling based on week status
    Color? containerColor;
    Border? containerBorder;

    switch (weekStatus) {
      case 'completed':
        containerColor = Colors.green.withOpacity(0.03);
        containerBorder = Border.all(color: Colors.green.withOpacity(0.1), width: 1);
        break;
      case 'current':
        containerColor = theme.colorScheme.primary.withOpacity(0.05);
        containerBorder = Border.all(color: theme.colorScheme.primary.withOpacity(0.15), width: 1);
        break;
      case 'active':
        containerColor = null;
        containerBorder = null;
        break;
      case 'locked':
      default:
        containerColor = isDarkMode ? Colors.grey.shade900.withOpacity(0.3) : Colors.grey.shade100;
        containerBorder = null;
        break;
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: containerColor,
        borderRadius: BorderRadius.circular(8),
        border: containerBorder,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: isLocked
              ? () {
                  // Show unlock date dialog for locked videos
                  _showUnlockDateDialog(video);
                }
              : () {
                  // Find the course this video belongs to
                  Course? videoCourse = _selectedCourse;
                  if (videoCourse == null) {
                    for (final course in _assignedCourses) {
                      if (_courseVideos.containsKey(course.id) &&
                          _courseVideos[course.id]!.any((v) => v.id == video.id)) {
                        videoCourse = course;
                        break;
                      }
                    }
                  }



                  // Open dedicated video player (no more overlay)
                  _openDedicatedVideoPlayer(video, videoCourse);

                  // Track the video access
                  _trackingService.saveLastAccessedVideo(video);
                  setState(() {
                    _lastAccessedVideo = video;
                  });

                  // If this video is from a different course than the active one,
                  // update the active course automatically
                  if (videoCourse != null && _lastOpenedCourse != null &&
                      videoCourse.id != _lastOpenedCourse!.id) {
                    // Update the active course silently
                    final Course nonNullCourse = videoCourse; // Create a non-nullable reference
                    setState(() {
                      _lastOpenedCourse = nonNullCourse;
                      _expandedCourses[nonNullCourse.id] = true;
                      // Track this as the last opened course
                      _trackingService.saveLastOpenedCourse(nonNullCourse);
                    });

                    // Show a toast notification
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Active course changed to "${videoCourse.title}"'),
                        duration: const Duration(seconds: 2),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  }
                },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Thumbnail with status indicator
                Stack(
                  children: [
                    // Thumbnail
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        width: 60,
                        height: 45,
                        color: isDarkMode ? Colors.black26 : Colors.grey[200],
                        child: video.thumbnailUrl != null
                            ? Image.network(
                                video.getFullThumbnailUrl(ApiService.baseUrl.replaceAll('/api', '')),
                                fit: BoxFit.cover,
                                loadingBuilder: (context, child, loadingProgress) {
                                  if (loadingProgress == null) return child;
                                  return Center(
                                    child: SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(
                                          theme.colorScheme.primary.withOpacity(0.5),
                                        ),
                                        value: loadingProgress.expectedTotalBytes != null
                                            ? loadingProgress.cumulativeBytesLoaded /
                                                loadingProgress.expectedTotalBytes!
                                            : null,
                                      ),
                                    ),
                                  );
                                },
                                errorBuilder: (context, error, stackTrace) {
                                  return Center(
                                    child: Icon(
                                      Icons.videocam_rounded,
                                      color: isDarkMode ? Colors.white38 : Colors.grey[400],
                                      size: 20,
                                    ),
                                  );
                                },
                              )
                            : Center(
                                child: Icon(
                                  Icons.videocam_rounded,
                                  color: isDarkMode ? Colors.white38 : Colors.grey[400],
                                  size: 20,
                                ),
                              ),
                      ),
                    ),

                    // Status overlay
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        width: 60,
                        height: 45,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Colors.black.withOpacity(isLocked ? 0.7 : 0.4),
                            ],
                            stops: const [0.5, 1.0],
                          ),
                        ),
                      ),
                    ),

                    // Status indicator
                    Positioned(
                      bottom: 2,
                      right: 2,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: isLocked
                              ? Colors.black.withOpacity(0.5)
                              : statusColor.withOpacity(0.2),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          isLocked
                              ? Icons.lock_outline_rounded
                              : isCompleted
                                  ? Icons.check_rounded
                                  : isLastAccessed
                                      ? Icons.play_circle_fill_rounded
                                      : Icons.play_circle_outline_rounded,
                          color: isLocked ? Colors.white : statusColor,
                          size: 12,
                        ),
                      ),
                    ),

                    // Duration badge
                    if (!isLocked && video.durationMinutes != null)
                      Positioned(
                        top: 2,
                        right: 2,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.6),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            '${video.durationMinutes}m',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 8,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),

                    // Last watched indicator
                    if (isLastAccessed)
                      Positioned(
                        top: 2,
                        left: 2,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Text(
                            'LAST',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 6,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(width: 12),

                // Video details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Title
                      Text(
                        video.title,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: isLocked
                              ? theme.colorScheme.onBackground.withOpacity(0.5)
                              : theme.colorScheme.onBackground,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),

                      // Info row
                      Row(
                        children: [
                          // Week label with status
                          _buildWeekStatusBadge(video.weekNumber, weekLabel, theme),
                          const SizedBox(width: 8),

                          // Status text
                          Text(
                            isLocked
                                ? 'Locked'
                                : isCompleted
                                    ? 'Completed'
                                    : isLastAccessed
                                        ? 'Last watched'
                                        : 'Ready',
                            style: theme.textTheme.bodySmall?.copyWith(
                              fontSize: 10,
                              color: statusColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),

                      // Progress indicator for partially watched videos
                      if (!isLocked && !isCompleted && video.watchDurationSeconds != null &&
                          video.watchDurationSeconds! > 0 && video.lastPositionSeconds != null) ...[
                        const SizedBox(height: 6),
                        Stack(
                          children: [
                            // Progress bar
                            ClipRRect(
                              borderRadius: BorderRadius.circular(2),
                              child: LinearProgressIndicator(
                                value: video.lastPositionSeconds! / video.watchDurationSeconds!,
                                minHeight: 3,
                                backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                                valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                              ),
                            ),

                            // Time indicator
                            Positioned(
                              right: 0,
                              top: -14,
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                                decoration: BoxDecoration(
                                  color: theme.colorScheme.primary.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(2),
                                ),
                                child: Text(
                                  '${(video.lastPositionSeconds! ~/ 60)}:${((video.lastPositionSeconds! % 60)).toString().padLeft(2, '0')}',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    fontSize: 8,
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),

                // Unlock date indicator (no action button needed since entire item is clickable)
                if (isLocked && video.unlockDate != null)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      _formatDate(video.unlockDate!),
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontSize: 10,
                        color: Colors.orange,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  )
                else if (!isLocked)
                  // Visual indicator only (no separate button needed)
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: isCompleted
                          ? Colors.green.withOpacity(0.1)
                          : theme.colorScheme.primary.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      isCompleted ? Icons.check_rounded : Icons.play_arrow_rounded,
                      color: isCompleted ? Colors.green : theme.colorScheme.primary,
                      size: 16,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showUnlockDateDialog(CourseVideo video) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        elevation: 8,
        backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        child: Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Lock icon with animation
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Icon(
                    Icons.lock_rounded,
                    size: 40,
                    color: Colors.orange,
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Title
              Text(
                'Content Locked',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onBackground,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),

              // Video title
              Text(
                video.title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onBackground.withOpacity(0.8),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),

              // Unlock date info
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.orange.withOpacity(0.1),
                      Colors.orange.withOpacity(0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.orange.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    Text(
                      video.unlockDate != null
                          ? 'This video will be available on:'
                          : 'This content is currently locked',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onBackground.withOpacity(0.7),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    if (video.unlockDate != null) ...[
                      const SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.event_available_rounded,
                            color: Colors.orange[700],
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _formatDate(video.unlockDate!),
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: Colors.orange[700],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Notify Me Button
              NotifyMeButton(
                video: video,
                onNotificationToggled: () {
                  // Optionally refresh the UI or perform other actions
                },
              ),
              const SizedBox(height: 16),

              // Close button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text('Close'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }

  // Build a visually distinct week badge based on week status
  Widget _buildWeekStatusBadge(int weekNumber, String weekLabel, ThemeData theme) {
    final weekStatus = _weekStatus[weekNumber] ?? 'locked';

    // Define colors and icons based on week status
    Color backgroundColor;
    Color textColor;
    IconData? icon;

    switch (weekStatus) {
      case 'completed':
        backgroundColor = Colors.green.withOpacity(0.15);
        textColor = Colors.green;
        icon = Icons.check_circle;
        break;
      case 'current':
        backgroundColor = theme.colorScheme.primary.withOpacity(0.2);
        textColor = theme.colorScheme.primary;
        icon = Icons.play_circle_filled;
        break;
      case 'active':
        backgroundColor = theme.colorScheme.primary.withOpacity(0.1);
        textColor = theme.colorScheme.primary;
        icon = null;
        break;
      case 'locked':
      default:
        backgroundColor = Colors.orange.withOpacity(0.1);
        textColor = Colors.orange;
        icon = Icons.lock_outline;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: textColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: 10,
              color: textColor,
            ),
            const SizedBox(width: 4),
          ],
          Text(
            weekLabel,
            style: theme.textTheme.bodySmall?.copyWith(
              fontSize: 10,
              color: textColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // Show a message when the user wants to manually change the active course
  void _showDifferentCourseMessage(Course? otherCourse) {
    if (_lastOpenedCourse == null || otherCourse == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Change Active Course'),
        content: Text(
          'Would you like to set "${otherCourse.title}" as your active course? '
          'This will help you track your progress more effectively.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Change the active course
              Navigator.of(context).pop();
              _setActiveCourse(otherCourse);
            },
            child: const Text('Set as Active'),
          ),
        ],
      ),
    );
  }

  // Helper method to set the active course
  void _setActiveCourse(Course course) {
    // Only update if the active course has changed
    if (_lastOpenedCourse == null || _lastOpenedCourse!.id != course.id) {
      setState(() {
        _lastOpenedCourse = course;
        _expandedCourses[course.id] = true;
      });

      // Track this as the last opened course (async without waiting)
      _trackingService.saveLastOpenedCourse(course);

      // Only load videos if we don't already have them
      if (!_courseVideos.containsKey(course.id) || _courseVideos[course.id]!.isEmpty) {
        _loadCourseVideos(course.id);
      } else {
        // Just recalculate week status for existing videos
        setState(() {
          _calculateWeekStatus(_courseVideos[course.id]!);
        });
      }

      // Show confirmation
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${course.title} is now your active course'),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  // Show confirmation dialog for changing the active course
  void _showResetCourseConfirmation(Course newCourse) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Change Active Course?'),
        content: Text(
          'Are you sure you want to change your active course to "${newCourse.title}"?',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _setActiveCourse(newCourse);
            },
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }

  // Process videos for display - Use homepage logic (trust API unlock status)
  List<CourseVideo> _processVideosForDisplay(List<CourseVideo> videos) {
    // Sort videos by week number first, then by sequence number within each week
    final sortedVideos = List<CourseVideo>.from(videos)
      ..sort((a, b) {
        int weekComparison = a.weekNumber.compareTo(b.weekNumber);
        if (weekComparison != 0) return weekComparison;
        return (a.sequenceNumber ?? 0).compareTo(b.sequenceNumber ?? 0);
      });

    // Use homepage logic: Trust the API unlock status directly
    // No client-side override of unlock status - let the server decide
    for (final video in sortedVideos) {
      debugPrint('Week ${video.weekNumber} Video ${video.sequenceNumber}: ${video.title} - Unlocked: ${video.isUnlocked} (API Response)');
    }

    return sortedVideos;
  }

  void _openDedicatedVideoPlayer(CourseVideo video, Course? videoCourse) async {
    try {
      // Get user profile for watermark
      final userProfile = await _getUserProfile();

      // Navigate to comprehensive video player page
      final result = await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ComprehensiveVideoPlayerPage(
            initialVideo: video,
            course: videoCourse,
            userProfile: userProfile,
            autoPlay: true,
          ),
        ),
      );

      // Refresh the course videos after returning from video player
      if (result != null && videoCourse != null) {
        _updateSingleVideo(result, videoCourse.id);
      } else if (videoCourse != null) {
        _loadCourseVideos(videoCourse.id);
      }

      // Update last opened course without full reload
      if (videoCourse != null) {
        setState(() {
          _lastOpenedCourse = videoCourse;
        });
      }
    } catch (e) {
      debugPrint('Failed to open dedicated video player: $e');
      // Fallback to showing error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to open video player: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<UserProfile?> _getUserProfile() async {
    try {
      final response = await _apiService.makeApiRequest('profile.php');
      if (response['success'] == true && response['profile'] != null) {
        return UserProfile.fromJson(response['profile']);
      }
      return null;
    } catch (e) {
      debugPrint('Failed to get user profile: $e');
      return null;
    }
  }
}
