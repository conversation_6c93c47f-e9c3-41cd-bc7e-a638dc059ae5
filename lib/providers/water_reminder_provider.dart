import 'dart:async';
import 'package:flutter/material.dart';
import '../models/water_reminder.dart';
import '../services/water_reminder_service.dart';
import '../services/awesome_notification_service.dart';

class WaterReminderProvider extends ChangeNotifier {
  final WaterReminderService _service = WaterReminderService();

  WaterReminder? _waterReminder;
  bool _isLoading = false;
  String? _error;
  Timer? _refreshTimer;

  // Getters
  WaterReminder? get waterReminder => _waterReminder;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Constructor
  WaterReminderProvider() {
    // Load water reminder data on initialization
    loadWaterReminder();

    // Set up periodic refresh (every 5 minutes)
    _refreshTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      refreshWaterReminder();
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  // Load water reminder data
  Future<void> loadWaterReminder() async {
    if (_isLoading) return;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final reminder = await _service.getWaterReminder();
      _waterReminder = reminder;
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to load water reminder data: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  // Refresh water reminder data from backend
  Future<void> refreshWaterReminder() async {
    try {
      final reminder = await _service.getWaterReminder();

      // Handle nullable reminder (admin may not have configured it)
      if (reminder == null) {
        // Check if user has their own settings
        final hasUserSettings = await _service.hasUserSettings();
        if (hasUserSettings) {
          final userReminder = await _service.getUserWaterReminder();
          _waterReminder = userReminder;
        } else {
          _waterReminder = null;
        }
        notifyListeners();
        return;
      }

      // Only update if there's a change
      if (_waterReminder == null ||
          _waterReminder!.lastUpdated != reminder.lastUpdated ||
          _waterReminder!.currentIntake != reminder.currentIntake ||
          _waterReminder!.targetIntake != reminder.targetIntake) {
        _waterReminder = reminder;
        notifyListeners();
      }
    } catch (e) {
      print('Error refreshing water reminder: $e');
      // Don't update error state on background refresh
    }
  }

  // Add water intake
  Future<void> addWaterIntake(int amount) async {
    try {
      final updatedReminder = await _service.updateWaterIntake(amount);
      _waterReminder = updatedReminder;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to update water intake: $e';
      notifyListeners();
    }
  }

  // Reset water intake
  Future<void> resetWaterIntake() async {
    try {
      final updatedReminder = await _service.resetWaterIntake();
      _waterReminder = updatedReminder;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to reset water intake: $e';
      notifyListeners();
    }
  }

  // Save user settings (when user configures their own settings)
  Future<void> saveUserSettings(WaterReminder reminder) async {
    try {
      await _service.saveUserSettings(reminder);
      _waterReminder = reminder;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to save user settings: $e';
      notifyListeners();
      rethrow;
    }
  }

  // Setup water reminder from user input (goal and interval in hours)
  Future<void> setupWaterReminder(int goal, int intervalHours) async {
    final now = DateTime.now();
    final reminder = WaterReminder(
      id: now.millisecondsSinceEpoch,
      title: 'Water Reminder',
      message: 'Time to drink water!',
      time: TimeOfDay(hour: now.hour, minute: now.minute),
      isEnabled: true,
      targetIntake: goal,
      currentIntake: 0,
      logs: [],
      lastUpdated: now,
    );
    await saveUserSettings(reminder);
    // Optionally, schedule notifications here if needed
    await updateNotificationInterval(intervalHours * 60);
  }

  // Update notification interval
  Future<void> updateNotificationInterval(int intervalMinutes) async {
    try {
      // Import the notification service
      final notificationService = EnhancedNotificationService();

      // Set the interval in hours (convert from minutes)
      final intervalHours = (intervalMinutes / 60).round().clamp(1, 24);
      await notificationService.setWaterReminderInterval(intervalHours);

      print('Updated water reminder interval to $intervalMinutes minutes ($intervalHours hours)');
    } catch (e) {
      print('Error updating notification interval: $e');
      // Don't throw error as this is not critical for saving settings
    }
  }

  // Get motivational message based on progress
  String getMotivationalMessage() {
    if (_waterReminder == null) return 'Stay hydrated!';

    final progress = _waterReminder!.progressPercentage;

    if (progress < 0.25) {
      return 'Start your day right! Drink some water now.';
    } else if (progress < 0.5) {
      return 'You\'re making progress! Keep drinking water.';
    } else if (progress < 0.75) {
      return 'More than halfway there! Keep it up!';
    } else if (progress < 1.0) {
      return 'Almost there! Just a bit more water to reach your goal.';
    } else {
      return 'Congratulations! You\'ve reached your water intake goal for today!';
    }
  }

  // Get water intake suggestion
  int getSuggestedIntake() {
    if (_waterReminder == null) return 250; // Default suggestion: 250ml

    // If user hasn't had any water yet, suggest a larger amount
    if (_waterReminder!.currentIntake == 0) {
      return 300; // Suggest 300ml to start the day
    }

    // If user is close to target, suggest the remaining amount
    final remaining = _waterReminder!.remainingIntake;
    if (remaining < 300) {
      return remaining;
    }

    // Otherwise suggest a standard glass of water (250ml)
    return 250;
  }

  // Get time until next reminder
  String getTimeUntilNextReminder() {
    if (_waterReminder == null) return 'Unknown';

    final nextReminder = _waterReminder!.getNextReminderTime();
    final now = DateTime.now();
    final difference = nextReminder.difference(now);

    final hours = difference.inHours;
    final minutes = difference.inMinutes % 60;

    if (hours > 0) {
      return '$hours hour${hours > 1 ? 's' : ''} and $minutes minute${minutes > 1 ? 's' : ''}';
    } else {
      return '$minutes minute${minutes > 1 ? 's' : ''}';
    }
  }
}
