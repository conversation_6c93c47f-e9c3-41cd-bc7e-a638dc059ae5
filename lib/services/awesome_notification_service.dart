import 'dart:io';
import 'dart:convert';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'global_notification_manager.dart';

class EnhancedNotificationService {
  static final EnhancedNotificationService _instance = EnhancedNotificationService._internal();
  factory EnhancedNotificationService() => _instance;
  EnhancedNotificationService._internal();

  bool _isInitialized = false;

  // Notification IDs
  static const int waterReminderId = 1000;
  static const int workoutReminderId = 2000;
  static const int customWorkoutReminderId = 3000;
  static const int testNotificationId = 9999;

  // Notification Channels
  static const String waterChannelId = 'water_reminder_channel';
  static const String workoutChannelId = 'workout_reminder_channel';
  static const String workoutVideosChannelId = 'workout_videos_channel';
  static const String generalChannelId = 'general_channel';
  static const String debugChannelId = 'scheduled';
  
  // SharedPreferences keys
  static const String _workoutNotificationsKey = 'workout_notifications_enabled';
  static const String _waterNotificationsKey = 'water_notifications_enabled';
  static const String _workoutTimeKey = 'workout_reminder_time';
  static const String _waterIntervalKey = 'water_reminder_interval';
  static const String _customWorkoutTimesKey = 'custom_workout_times';

  bool get isInitialized => _isInitialized;

  /// Initialize Enhanced Notifications
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      print('🔔 Initializing Enhanced Notifications...');

      // Initialize timezone data
      tz_data.initializeTimeZones();

      // Initialize AwesomeNotifications
      await AwesomeNotifications().initialize(
        null, // Use default app icon
        [
          NotificationChannel(
            channelKey: waterChannelId,
            channelName: 'Water Reminders',
            channelDescription: 'Notifications for water intake reminders',
            defaultColor: const Color(0xFF06B6D4),
            ledColor: const Color(0xFF06B6D4),
            importance: NotificationImportance.High,
            channelShowBadge: true,
            onlyAlertOnce: false,
            playSound: true,
            criticalAlerts: true,
            icon: 'resource://drawable/ic_notification',
          ),
          NotificationChannel(
            channelKey: workoutChannelId,
            channelName: 'Workout Reminders',
            channelDescription: 'Notifications for workout reminders',
            defaultColor: const Color(0xFFFF6B35),
            ledColor: const Color(0xFFFF6B35),
            importance: NotificationImportance.High,
            channelShowBadge: true,
            onlyAlertOnce: false,
            playSound: true,
            criticalAlerts: true,
            icon: 'resource://drawable/ic_notification',
          ),
          NotificationChannel(
            channelKey: workoutVideosChannelId,
            channelName: 'Workout Videos',
            channelDescription: 'Notifications for new workout videos and unlocks',
            defaultColor: const Color(0xFF8B5CF6),
            ledColor: const Color(0xFF8B5CF6),
            importance: NotificationImportance.High,
            channelShowBadge: true,
            onlyAlertOnce: false,
            playSound: true,
            criticalAlerts: false,
            icon: 'resource://drawable/ic_notification',
          ),
          NotificationChannel(
            channelKey: generalChannelId,
            channelName: 'General Notifications',
            channelDescription: 'General app notifications',
            defaultColor: Colors.green,
            ledColor: Colors.green,
            importance: NotificationImportance.Default,
            channelShowBadge: true,
            onlyAlertOnce: false,
            playSound: true,
          ),
          NotificationChannel(
            channelKey: debugChannelId,
            channelName: 'Debug Notifications',
            channelDescription: 'Debug and test notifications',
            defaultColor: Colors.purple,
            ledColor: Colors.purple,
            importance: NotificationImportance.High,
            channelShowBadge: true,
            onlyAlertOnce: false,
            playSound: true,
          ),
        ],
        debug: true,
      );

      _isInitialized = true;
      print('✅ Enhanced Notifications initialized successfully');
    } catch (e) {
      print('❌ Error initializing Enhanced Notifications: $e');
      rethrow;
    }
  }

  /// Create notification channels for Android
  Future<void> _createNotificationChannels() async {
    // Channels are created during initialization
  }

  /// Request notification permissions
  Future<bool> requestPermissions() async {
    try {
      print('🔔 Requesting notification permissions...');

      // Request basic notification permission
      bool isAllowed = await AwesomeNotifications().isNotificationAllowed();

      if (!isAllowed) {
        isAllowed = await AwesomeNotifications().requestPermissionToSendNotifications();
      }

      if (isAllowed) {
        // Request additional Android permissions
        if (Platform.isAndroid) {
          // Request exact alarm permission for Android 12+
          await Permission.scheduleExactAlarm.request();

          // Request ignore battery optimization
          await Permission.ignoreBatteryOptimizations.request();

          // Request notification policy access
          await Permission.accessNotificationPolicy.request();
        }
      }

      print('🔔 Notification permissions result: $isAllowed');
      return isAllowed;
    } catch (e) {
      print('❌ Error requesting notification permissions: $e');
      return false;
    }
  }

  /// Check if notifications are allowed
  Future<bool> hasPermissions() async {
    try {
      return await AwesomeNotifications().isNotificationAllowed();
    } catch (e) {
      print('❌ Error checking notification permissions: $e');
      return false;
    }
  }

  /// Cancel all notifications
  Future<void> cancelAllNotifications() async {
    try {
      await AwesomeNotifications().cancelAll();
      print('🔔 All notifications cancelled');
    } catch (e) {
      print('❌ Error cancelling all notifications: $e');
    }
  }

  /// Cancel notification by ID
  Future<void> cancelNotification(int id) async {
    try {
      await AwesomeNotifications().cancel(id);
      print('🔔 Notification $id cancelled');
    } catch (e) {
      print('❌ Error cancelling notification $id: $e');
    }
  }

  /// Get scheduled notifications
  Future<List<Map<String, dynamic>>> getScheduledNotifications() async {
    try {
      final notifications = await AwesomeNotifications().listScheduledNotifications();
      return notifications.map((n) => {
        'id': n.content?.id,
        'title': n.content?.title,
        'body': n.content?.body,
        'channelKey': n.content?.channelKey,
        'scheduledDate': n.schedule?.toString(),
      }).toList();
    } catch (e) {
      print('❌ Error getting scheduled notifications: $e');
      return [];
    }
  }

  // ==================== WATER REMINDER METHODS ====================

  /// Schedule water reminders
  Future<void> scheduleWaterReminders() async {
    try {
      // Cancel existing water reminders first
      await _cancelWaterReminders();

      final intervalHours = await getWaterReminderInterval();
      final now = DateTime.now();

      // Schedule for next 7 days
      for (int day = 0; day < 7; day++) {
        final targetDay = now.add(Duration(days: day));
        await _scheduleWaterRemindersForDay(targetDay, intervalHours);
      }

      print('🔔 Water reminders scheduled for next 7 days');
    } catch (e) {
      print('❌ Error scheduling water reminders: $e');
    }
  }

  /// Schedule water reminders for a specific day
  Future<void> _scheduleWaterRemindersForDay(DateTime day, int intervalHours) async {
    try {
      // Start from 8 AM, end at 10 PM
      final startHour = 8;
      final endHour = 22;

      for (int hour = startHour; hour <= endHour; hour += intervalHours) {
        final scheduledTime = DateTime(day.year, day.month, day.day, hour, 0);

        // Only schedule future notifications
        if (scheduledTime.isAfter(DateTime.now())) {
          final id = waterReminderId + (day.day * 100) + hour;
          await _scheduleWaterReminder(id: id, scheduledTime: scheduledTime);
        }
      }
    } catch (e) {
      print('❌ Error scheduling water reminders for day: $e');
    }
  }

  /// Schedule a single water reminder
  Future<void> _scheduleWaterReminder({
    required int id,
    required DateTime scheduledTime,
  }) async {
    try {
      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: id,
          channelKey: waterChannelId,
          title: '💧 Time to Hydrate!',
          body: 'Don\'t forget to drink water and stay hydrated!',
          notificationLayout: NotificationLayout.Default,
          wakeUpScreen: true,
          category: NotificationCategory.Reminder,
        ),
        schedule: NotificationCalendar.fromDate(date: scheduledTime),
      );
    } catch (e) {
      print('❌ Error scheduling water reminder: $e');
    }
  }

  /// Schedule water snooze reminder
  Future<void> _scheduleWaterSnooze() async {
    try {
      final snoozeTime = DateTime.now().add(const Duration(minutes: 10));
      await _scheduleWaterReminder(id: waterReminderId + 9999, scheduledTime: snoozeTime);
    } catch (e) {
      print('❌ Error scheduling water snooze: $e');
    }
  }

  /// Cancel all water reminders
  Future<void> _cancelWaterReminders() async {
    try {
      // Cancel water reminder range
      for (int i = waterReminderId; i < waterReminderId + 10000; i++) {
        await AwesomeNotifications().cancel(i);
      }
      print('🔔 Water reminders cancelled');
    } catch (e) {
      print('❌ Error cancelling water reminders: $e');
    }
  }

  /// Cancel all water reminders (public method)
  Future<void> cancelWaterReminders() async {
    await _cancelWaterReminders();
  }

  // Water reminder settings methods
  Future<bool> isWaterNotificationEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_waterNotificationsKey) ?? false;
    } catch (e) {
      print('❌ Error checking water notification status: $e');
      return false;
    }
  }

  Future<void> setWaterNotificationEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_waterNotificationsKey, enabled);

      if (enabled) {
        await scheduleWaterReminders();
      } else {
        await cancelWaterReminders();
      }

      print('🔔 Water notifications ${enabled ? 'enabled' : 'disabled'}');
    } catch (e) {
      print('❌ Error setting water notification status: $e');
    }
  }

  Future<int> getWaterReminderInterval() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_waterIntervalKey) ?? 2; // Default 2 hours
    } catch (e) {
      print('❌ Error getting water reminder interval: $e');
      return 2;
    }
  }

  Future<void> setWaterReminderInterval(int hours) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_waterIntervalKey, hours);

      // Reschedule if notifications are enabled
      if (await isWaterNotificationEnabled()) {
        await scheduleWaterReminders();
      }

      print('🔔 Water reminder interval set to $hours hours');
    } catch (e) {
      print('❌ Error setting water reminder interval: $e');
    }
  }

  // ==================== WORKOUT REMINDER METHODS ====================

  /// Schedule workout reminder
  Future<void> scheduleWorkoutReminder() async {
    try {
      // Cancel existing workout reminders first
      await _cancelWorkoutReminders();

      final timeString = await getWorkoutReminderTime();
      final timeParts = timeString.split(':');
      final hour = int.parse(timeParts[0]);
      final minute = int.parse(timeParts[1]);

      final now = DateTime.now();

      // Schedule for next 7 days
      for (int day = 0; day < 7; day++) {
        final targetDay = now.add(Duration(days: day));
        await _scheduleWorkoutReminderForDay(targetDay, hour, minute);
      }

      print('🔔 Workout reminders scheduled for next 7 days at $timeString');
    } catch (e) {
      print('❌ Error scheduling workout reminders: $e');
    }
  }

  /// Schedule workout reminder for a specific day
  Future<void> _scheduleWorkoutReminderForDay(DateTime day, int hour, int minute) async {
    try {
      final scheduledTime = DateTime(day.year, day.month, day.day, hour, minute);

      // Only schedule future notifications
      if (scheduledTime.isAfter(DateTime.now())) {
        await _scheduleWorkoutReminderNotification(scheduledTime);
      }
    } catch (e) {
      print('❌ Error scheduling workout reminder for day: $e');
    }
  }

  /// Schedule a single workout reminder notification
  Future<void> _scheduleWorkoutReminderNotification(DateTime scheduledTime) async {
    try {
      final id = workoutReminderId + scheduledTime.day;

      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: id,
          channelKey: workoutChannelId,
          title: '💪 Workout Time!',
          body: 'Time for your daily workout. Let\'s get moving!',
          notificationLayout: NotificationLayout.Default,
          wakeUpScreen: true,
          category: NotificationCategory.Reminder,
        ),
        schedule: NotificationCalendar.fromDate(date: scheduledTime),
      );
    } catch (e) {
      print('❌ Error scheduling workout reminder notification: $e');
    }
  }

  /// Schedule workout snooze reminder
  Future<void> _scheduleWorkoutSnooze() async {
    try {
      final snoozeTime = DateTime.now().add(const Duration(minutes: 15));
      await _scheduleWorkoutReminderNotification(snoozeTime);
    } catch (e) {
      print('❌ Error scheduling workout snooze: $e');
    }
  }

  /// Cancel all workout reminders
  Future<void> _cancelWorkoutReminders() async {
    try {
      // Cancel workout reminder range
      for (int i = workoutReminderId; i < workoutReminderId + 1000; i++) {
        await AwesomeNotifications().cancel(i);
      }
      print('🔔 Workout reminders cancelled');
    } catch (e) {
      print('❌ Error cancelling workout reminders: $e');
    }
  }

  /// Cancel all workout reminders (public method)
  Future<void> cancelWorkoutReminders() async {
    await _cancelWorkoutReminders();
  }

  // Workout reminder settings methods
  Future<bool> isWorkoutNotificationEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_workoutNotificationsKey) ?? false;
    } catch (e) {
      print('❌ Error checking workout notification status: $e');
      return false;
    }
  }

  Future<void> setWorkoutNotificationEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_workoutNotificationsKey, enabled);

      if (enabled) {
        await scheduleWorkoutReminder();
      } else {
        await cancelWorkoutReminders();
      }

      print('🔔 Workout notifications ${enabled ? 'enabled' : 'disabled'}');
    } catch (e) {
      print('❌ Error setting workout notification status: $e');
    }
  }

  Future<String> getWorkoutReminderTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_workoutTimeKey) ?? '18:00'; // Default 6 PM
    } catch (e) {
      print('❌ Error getting workout reminder time: $e');
      return '18:00';
    }
  }

  Future<void> setWorkoutReminderTime(String time) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_workoutTimeKey, time);

      // Reschedule if notifications are enabled
      if (await isWorkoutNotificationEnabled()) {
        await scheduleWorkoutReminder();
      }

      print('🔔 Workout reminder time set to $time');
    } catch (e) {
      print('❌ Error setting workout reminder time: $e');
    }
  }

  // ==================== CUSTOM WORKOUT TIMES ====================

  /// Get custom workout times
  Future<List<Map<String, dynamic>>> getCustomWorkoutTimes() async {
    // Implementation needed
    return [];
  }

  /// Set custom workout times
  Future<void> setCustomWorkoutTimes(List<Map<String, dynamic>> workoutTimes) async {
    // Implementation needed
  }

  /// Schedule custom workout reminders
  Future<void> scheduleCustomWorkoutReminders() async {
    // Implementation needed
  }

  /// Schedule a single custom workout reminder
  Future<void> _scheduleCustomWorkoutReminder(Map<String, dynamic> workoutTime, int index) async {
    // Implementation needed
  }

  /// Cancel all custom workout reminders
  Future<void> _cancelCustomWorkoutReminders() async {
    // Implementation needed
  }

  // ==================== GENERAL NOTIFICATION METHODS ====================

  /// Show a notification immediately
  Future<void> showNotification({
    required int id,
    required String channelKey,
    required String title,
    required String body,
    String? summary,
    NotificationLayout? notificationLayout,
    NotificationCategory? category,
    Map<String, String>? payload,
    bool wakeUpScreen = true,
  }) async {
    try {
      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: id,
          channelKey: channelKey,
          title: title,
          body: body,
          summary: summary,
          notificationLayout: notificationLayout ?? NotificationLayout.Default,
          wakeUpScreen: wakeUpScreen,
          category: category ?? NotificationCategory.Message,
          payload: payload,
        ),
      );
      print('🔔 Notification sent: $title');
    } catch (e) {
      print('❌ Error sending notification: $e');
      rethrow;
    }
  }

  // ==================== DEBUG AND TESTING METHODS ====================



  /// Send a 16-second test notification
  Future<void> send16SecondTest() async {
    try {
      print('🔔 Starting 16-second test notification...');

      // Check if notifications are allowed first
      final isAllowed = await AwesomeNotifications().isNotificationAllowed();
      print('🔔 Notification permission status: $isAllowed');

      if (!isAllowed) {
        print('❌ Notifications not allowed, requesting permission...');
        final granted = await AwesomeNotifications().requestPermissionToSendNotifications();
        print('🔔 Permission request result: $granted');
        if (!granted) {
          throw Exception('Notification permission denied');
        }
      }

      final scheduledTime = DateTime.now().add(const Duration(seconds: 16));
      print('🔔 Scheduling notification for: ${scheduledTime.toString()}');

      final success = await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: testNotificationId + 16,
          channelKey: debugChannelId,
          title: '⏰ 16-Second Test',
          body: 'This notification was scheduled 16 seconds ago! 🎉',
          notificationLayout: NotificationLayout.Default,
          wakeUpScreen: true,
          category: NotificationCategory.Reminder,
          criticalAlert: true,
        ),
        schedule: NotificationCalendar.fromDate(date: scheduledTime),
      );

      print('🔔 Notification creation result: $success');
      print('✅ 16-second test notification scheduled successfully for ${scheduledTime.toString()}');
    } catch (e, stackTrace) {
      print('❌ Error scheduling 16-second test: $e');
      print('❌ Stack trace: $stackTrace');
      rethrow;
    }
  }

  /// Send a 17-second test notification
  Future<void> send17SecondTest() async {
    try {
      print('🔔 Starting 17-second test notification...');

      // Check if notifications are allowed first
      final isAllowed = await AwesomeNotifications().isNotificationAllowed();
      print('🔔 Notification permission status: $isAllowed');

      if (!isAllowed) {
        print('❌ Notifications not allowed, requesting permission...');
        final granted = await AwesomeNotifications().requestPermissionToSendNotifications();
        print('🔔 Permission request result: $granted');
        if (!granted) {
          throw Exception('Notification permission denied');
        }
      }

      final scheduledTime = DateTime.now().add(const Duration(seconds: 17));
      print('🔔 Scheduling notification for: ${scheduledTime.toString()}');

      final success = await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: testNotificationId + 17,
          channelKey: debugChannelId,
          title: '⏰ 17-Second Test',
          body: 'This notification was scheduled 17 seconds ago! 🚀',
          notificationLayout: NotificationLayout.Default,
          wakeUpScreen: true,
          category: NotificationCategory.Reminder,
          criticalAlert: true,
        ),
        schedule: NotificationCalendar.fromDate(date: scheduledTime),
      );

      print('🔔 Notification creation result: $success');
      print('✅ 17-second test notification scheduled successfully for ${scheduledTime.toString()}');
    } catch (e, stackTrace) {
      print('❌ Error scheduling 17-second test: $e');
      print('❌ Stack trace: $stackTrace');
      rethrow;
    }
  }

  /// Send a 21-second water reminder test notification
  Future<void> send21SecondTest() async {
    try {
      print('💧 Starting 21-second water reminder test notification...');

      // Check if notifications are allowed first
      final isAllowed = await AwesomeNotifications().isNotificationAllowed();
      print('🔔 Notification permission status: $isAllowed');

      if (!isAllowed) {
        print('❌ Notifications not allowed, requesting permission...');
        final granted = await AwesomeNotifications().requestPermissionToSendNotifications();
        print('🔔 Permission request result: $granted');
        if (!granted) {
          throw Exception('Notification permission denied');
        }
      }

      final scheduledTime = DateTime.now().add(const Duration(seconds: 21));
      print('💧 Scheduling water reminder test for: ${scheduledTime.toString()}');

      final success = await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: testNotificationId + 21,
          channelKey: waterChannelId, // Use water reminder channel
          title: '💧 21-Second Water Test',
          body: 'This water reminder was scheduled 21 seconds ago! 💧',
          notificationLayout: NotificationLayout.Default,
          wakeUpScreen: true,
          category: NotificationCategory.Reminder,
          criticalAlert: true,
        ),
        schedule: NotificationCalendar.fromDate(date: scheduledTime),
      );

      print('🔔 Notification creation result: $success');
      print('✅ 21-second water test notification scheduled successfully for ${scheduledTime.toString()}');
    } catch (e, stackTrace) {
      print('❌ Error scheduling 21-second water test: $e');
      print('❌ Stack trace: $stackTrace');
      rethrow;
    }
  }



  /// Monitor all notification strategies
  void _startStrategyMonitoring() {
    // Implementation needed
  }

  /// Clean up test notifications
  Future<void> _cleanupTestNotifications() async {
    // Implementation needed
  }

  /// Get active notifications from system
  Future<List<Map<String, dynamic>>> _getActiveNotifications() async {
    // Implementation needed
    return [];
  }

  /// Alternative scheduling method for test notification
  Future<void> _scheduleAlternativeTestNotification(DateTime scheduledTime) async {
    // Implementation needed
  }

  /// Clear all scheduled notifications
  Future<void> clearAllScheduledNotifications() async {
    try {
      await AwesomeNotifications().cancelAllSchedules();
      print('🔔 All scheduled notifications cleared');
    } catch (e) {
      print('❌ Error clearing scheduled notifications: $e');
    }
  }

  /// Check Android notification settings and permissions thoroughly
  Future<void> checkAndroidNotificationSettings() async {
    try {
      final isAllowed = await AwesomeNotifications().isNotificationAllowed();
      print('🔔 Notification permission: $isAllowed');

      if (Platform.isAndroid) {
        final exactAlarmStatus = await Permission.scheduleExactAlarm.status;
        final batteryOptStatus = await Permission.ignoreBatteryOptimizations.status;

        print('🔔 Exact alarm permission: $exactAlarmStatus');
        print('🔔 Battery optimization permission: $batteryOptStatus');
      }
    } catch (e) {
      print('❌ Error checking Android notification settings: $e');
    }
  }

  /// Check for system-level notification restrictions
  Future<void> _checkSystemNotificationRestrictions() async {
    try {
      final globalSettings = await AwesomeNotifications().checkPermissionList();
      print('🔔 Global notification settings: $globalSettings');
    } catch (e) {
      print('❌ Error checking system restrictions: $e');
    }
  }

  /// Get notification permission status details
  Future<Map<String, dynamic>> getPermissionStatus() async {
    try {
      final isAllowed = await AwesomeNotifications().isNotificationAllowed();
      final globalSettings = await AwesomeNotifications().checkPermissionList();

      Map<String, dynamic> status = {
        'notifications_allowed': isAllowed,
        'global_settings': globalSettings,
      };

      if (Platform.isAndroid) {
        final exactAlarmStatus = await Permission.scheduleExactAlarm.status;
        final batteryOptStatus = await Permission.ignoreBatteryOptimizations.status;
        final notificationPolicyStatus = await Permission.accessNotificationPolicy.status;

        status['exact_alarm'] = exactAlarmStatus.name;
        status['battery_optimization'] = batteryOptStatus.name;
        status['notification_policy'] = notificationPolicyStatus.name;
      }

      return status;
    } catch (e) {
      print('❌ Error getting permission status: $e');
      return {'error': e.toString()};
    }
  }

  /// Initialize all notifications based on current preferences
  Future<void> initializeNotifications() async {
    try {
      print('🔔 Initializing notifications based on preferences...');

      if (await isWaterNotificationEnabled()) {
        await scheduleWaterReminders();
      }

      if (await isWorkoutNotificationEnabled()) {
        await scheduleWorkoutReminder();
      }

      print('✅ Notifications initialized successfully');
    } catch (e) {
      print('❌ Error initializing notifications: $e');
    }
  }

  /// Comprehensive Android system diagnostic for scheduled notifications
  Future<Map<String, dynamic>> performSystemDiagnostic() async {
    try {
      final permissionStatus = await getPermissionStatus();
      final scheduledNotifications = await getScheduledNotifications();

      return {
        'timestamp': DateTime.now().toIso8601String(),
        'permissions': permissionStatus,
        'scheduled_count': scheduledNotifications.length,
        'scheduled_notifications': scheduledNotifications,
        'water_enabled': await isWaterNotificationEnabled(),
        'workout_enabled': await isWorkoutNotificationEnabled(),
        'water_interval': await getWaterReminderInterval(),
        'workout_time': await getWorkoutReminderTime(),
      };
    } catch (e) {
      print('❌ Error performing system diagnostic: $e');
      return {'error': e.toString()};
    }
  }
}
