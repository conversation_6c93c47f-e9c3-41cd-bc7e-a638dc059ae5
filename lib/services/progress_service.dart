import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/course.dart';
import '../models/course_video.dart';
import '../services/api_service.dart';
import '../services/video_streak_service.dart';

/// Comprehensive progress tracking service with real-time updates and data synchronization
class ProgressService extends ChangeNotifier {
  static final ProgressService _instance = ProgressService._internal();
  factory ProgressService() => _instance;
  ProgressService._internal();

  // Storage keys
  static const String _courseProgressKey = 'course_progress_data';
  static const String _videoProgressKey = 'video_progress_data';
  static const String _overallStatsKey = 'overall_stats_data';
  static const String _lastSyncKey = 'last_progress_sync';

  // Services
  final ApiService _apiService = ApiService();
  final VideoStreakService _streakService = VideoStreakService();

  // Progress data
  Map<int, CourseProgress> _courseProgress = {};
  Map<int, VideoProgress> _videoProgress = {};
  OverallStats _overallStats = OverallStats();
  DateTime? _lastSyncTime;

  // Stream controllers for real-time updates
  final StreamController<Map<int, CourseProgress>> _courseProgressController =
      StreamController<Map<int, CourseProgress>>.broadcast();
  final StreamController<Map<int, VideoProgress>> _videoProgressController =
      StreamController<Map<int, VideoProgress>>.broadcast();
  final StreamController<OverallStats> _overallStatsController =
      StreamController<OverallStats>.broadcast();

  // Getters
  Map<int, CourseProgress> get courseProgress => Map.unmodifiable(_courseProgress);
  Map<int, VideoProgress> get videoProgress => Map.unmodifiable(_videoProgress);
  OverallStats get overallStats => _overallStats;
  DateTime? get lastSyncTime => _lastSyncTime;

  // Streams
  Stream<Map<int, CourseProgress>> get courseProgressStream => _courseProgressController.stream;
  Stream<Map<int, VideoProgress>> get videoProgressStream => _videoProgressController.stream;
  Stream<OverallStats> get overallStatsStream => _overallStatsController.stream;

  /// Initialize the progress service
  Future<void> initialize() async {
    try {
      print('🔄 DEBUG: Initializing ProgressService...');
      await _loadFromLocalStorage();

      // Add some test data if no data exists
      if (_videoProgress.isEmpty) {
        await _addTestData();
      }

      await _syncWithBackend();
      await _updateOverallStats();

      print('✅ DEBUG: ProgressService initialized successfully');
      print('📊 DEBUG: Loaded ${_videoProgress.length} video progress records');
      print('📊 DEBUG: Overall stats - Videos: ${_overallStats.totalVideosWatched}, Time: ${_overallStats.totalWatchTimeSeconds}s');
    } catch (e) {
      print('❌ DEBUG: Error initializing ProgressService: $e');
    }
  }

  /// Add test data for development/testing
  Future<void> _addTestData() async {
    try {
      print('🧪 Adding test data...');
      final now = DateTime.now();

      // Add some test video progress for today
      for (int i = 1; i <= 3; i++) {
        final watchDuration = 1800 + (i * 300); // 30-45 minutes
        final testProgress = VideoProgress(
          videoId: i,
          courseId: 1,
          progressPercentage: 100.0,
          watchDurationSeconds: watchDuration,
          lastPositionSeconds: watchDuration, // Completed videos
          totalDurationSeconds: watchDuration,
          lastUpdated: now.subtract(Duration(hours: i)),
          isCompleted: true,
        );
        _videoProgress[i] = testProgress;
      }

      // Add some test data for previous days
      for (int day = 1; day <= 6; day++) {
        final dayDate = now.subtract(Duration(days: day));
        for (int i = 1; i <= (7 - day); i++) {
          final videoId = (day * 10) + i;
          final watchDuration = 1200 + (i * 200);
          final testProgress = VideoProgress(
            videoId: videoId,
            courseId: 1,
            progressPercentage: 100.0,
            watchDurationSeconds: watchDuration,
            lastPositionSeconds: watchDuration,
            totalDurationSeconds: watchDuration,
            lastUpdated: dayDate.subtract(Duration(hours: i)),
            isCompleted: true,
          );
          _videoProgress[videoId] = testProgress;
        }
      }

      await _saveToLocalStorage();
      print('🧪 Test data added: ${_videoProgress.length} video progress records');
    } catch (e) {
      print('❌ Error adding test data: $e');
    }
  }

  /// Update video progress with real-time synchronization
  Future<void> updateVideoProgress({
    required int videoId,
    int? courseId,
    int? watchDurationSeconds,
    int? lastPositionSeconds,
    bool? isCompleted,
    int? totalDurationSeconds,
  }) async {
    try {
      print('🎬 DEBUG: Updating video progress - Video: $videoId, Completed: $isCompleted');

      // Update local progress immediately for responsive UI
      final currentProgress = _videoProgress[videoId] ?? VideoProgress(
        videoId: videoId,
        courseId: courseId ?? 0,
        watchDurationSeconds: 0,
        lastPositionSeconds: 0,
        isCompleted: false,
        totalDurationSeconds: totalDurationSeconds ?? 0,
        progressPercentage: 0.0,
        lastUpdated: DateTime.now(),
      );

      final updatedProgress = currentProgress.copyWith(
        watchDurationSeconds: watchDurationSeconds ?? currentProgress.watchDurationSeconds,
        lastPositionSeconds: lastPositionSeconds ?? currentProgress.lastPositionSeconds,
        isCompleted: isCompleted ?? currentProgress.isCompleted,
        totalDurationSeconds: totalDurationSeconds ?? currentProgress.totalDurationSeconds,
        lastUpdated: DateTime.now(),
      );

      // Calculate progress percentage
      if (updatedProgress.totalDurationSeconds > 0) {
        updatedProgress.progressPercentage =
            (updatedProgress.watchDurationSeconds / updatedProgress.totalDurationSeconds * 100)
            .clamp(0.0, 100.0);
      }

      _videoProgress[videoId] = updatedProgress;

      // Update course progress if courseId is provided
      if (courseId != null) {
        await _updateCourseProgress(courseId);
      }

      // Update overall stats
      await _updateOverallStats();

      // Save to local storage
      await _saveToLocalStorage();

      // Notify listeners immediately
      _videoProgressController.add(Map.unmodifiable(_videoProgress));
      notifyListeners();

      // Sync with backend in background
      _syncVideoProgressWithBackend(videoId, updatedProgress);

      // Update streak if video is completed
      if (isCompleted == true) {
        await _updateStreakForVideoCompletion(videoId);
      }

      print('✅ DEBUG: Video progress updated successfully');
    } catch (e) {
      print('❌ DEBUG: Error updating video progress: $e');
      rethrow;
    }
  }

  /// Update course progress based on video completions
  Future<void> _updateCourseProgress(int courseId) async {
    try {
      // Get all videos for this course
      final courseVideos = _videoProgress.values
          .where((progress) => progress.courseId == courseId)
          .toList();

      if (courseVideos.isEmpty) return;

      final totalVideos = courseVideos.length;
      final completedVideos = courseVideos.where((v) => v.isCompleted).length;
      final totalWatchTime = courseVideos.fold<int>(
          0, (sum, v) => sum + v.watchDurationSeconds);
      final progressPercentage = totalVideos > 0
          ? (completedVideos / totalVideos * 100).round()
          : 0;

      final courseProgress = CourseProgress(
        courseId: courseId,
        totalVideos: totalVideos,
        completedVideos: completedVideos,
        progressPercentage: progressPercentage,
        totalWatchTimeSeconds: totalWatchTime,
        lastUpdated: DateTime.now(),
      );

      _courseProgress[courseId] = courseProgress;
      _courseProgressController.add(Map.unmodifiable(_courseProgress));

      print('📊 DEBUG: Course $courseId progress: $completedVideos/$totalVideos ($progressPercentage%)');
    } catch (e) {
      print('❌ DEBUG: Error updating course progress: $e');
    }
  }

  /// Update overall statistics
  Future<void> _updateOverallStats() async {
    try {
      final totalCourses = _courseProgress.length;
      final completedCourses = _courseProgress.values
          .where((course) => course.progressPercentage >= 100)
          .length;
      final totalVideosWatched = _videoProgress.values
          .where((video) => video.isCompleted)
          .length;
      final totalWatchTime = _videoProgress.values
          .fold<int>(0, (sum, video) => sum + video.watchDurationSeconds);

      _overallStats = OverallStats(
        totalCourses: totalCourses,
        completedCourses: completedCourses,
        totalVideosWatched: totalVideosWatched,
        totalWatchTimeSeconds: totalWatchTime,
        lastUpdated: DateTime.now(),
      );

      _overallStatsController.add(_overallStats);
      print('📈 DEBUG: Overall stats updated - Courses: $completedCourses/$totalCourses, Videos: $totalVideosWatched');
    } catch (e) {
      print('❌ DEBUG: Error updating overall stats: $e');
    }
  }

  /// Update streak for video completion
  Future<void> _updateStreakForVideoCompletion(int videoId) async {
    try {
      final videoProgress = _videoProgress[videoId];
      if (videoProgress != null && videoProgress.progressPercentage >= 50.0) {
        // Update streak for video completion using VideoStreakService
        try {
          final result = await _streakService.processVideoCompletion(
            videoId,
            videoProgress.watchDurationSeconds,
            videoProgress.totalDurationSeconds ~/ 60, // Convert seconds to minutes
          );

          if (result.streakUpdated) {
            print('🔥 DEBUG: Streak updated for video completion - New streak: ${result.newStreak}');
          } else {
            print('🔥 DEBUG: Video completion processed - Current streak: ${result.newStreak}');
          }
        } catch (e) {
          print('❌ DEBUG: Error updating streak: $e');
        }
      }
    } catch (e) {
      print('❌ DEBUG: Error updating streak: $e');
    }
  }

  /// Sync video progress with backend
  Future<void> _syncVideoProgressWithBackend(int videoId, VideoProgress progress) async {
    try {
      await _apiService.updateVideoProgress(
        videoId: videoId,
        watchDurationSeconds: progress.watchDurationSeconds,
        lastPositionSeconds: progress.lastPositionSeconds,
        isCompleted: progress.isCompleted,
      );
      print('🔄 DEBUG: Video $videoId synced with backend');
    } catch (e) {
      print('❌ DEBUG: Failed to sync video $videoId with backend: $e');
      // Don't rethrow - allow local progress to continue
    }
  }

  /// Load progress data from local storage
  Future<void> _loadFromLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load course progress
      final courseProgressJson = prefs.getString(_courseProgressKey);
      if (courseProgressJson != null) {
        final Map<String, dynamic> courseData = jsonDecode(courseProgressJson);
        _courseProgress = courseData.map((key, value) =>
            MapEntry(int.parse(key), CourseProgress.fromJson(value)));
      }

      // Load video progress
      final videoProgressJson = prefs.getString(_videoProgressKey);
      if (videoProgressJson != null) {
        final Map<String, dynamic> videoData = jsonDecode(videoProgressJson);
        _videoProgress = videoData.map((key, value) =>
            MapEntry(int.parse(key), VideoProgress.fromJson(value)));
      }

      // Load overall stats
      final overallStatsJson = prefs.getString(_overallStatsKey);
      if (overallStatsJson != null) {
        _overallStats = OverallStats.fromJson(jsonDecode(overallStatsJson));
      }

      // Load last sync time
      final lastSyncMillis = prefs.getInt(_lastSyncKey);
      if (lastSyncMillis != null) {
        _lastSyncTime = DateTime.fromMillisecondsSinceEpoch(lastSyncMillis);
      }

      print('📱 DEBUG: Progress data loaded from local storage');
    } catch (e) {
      print('❌ DEBUG: Error loading from local storage: $e');
    }
  }

  /// Save progress data to local storage
  Future<void> _saveToLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save course progress
      final courseProgressJson = jsonEncode(_courseProgress.map((key, value) =>
          MapEntry(key.toString(), value.toJson())));
      await prefs.setString(_courseProgressKey, courseProgressJson);

      // Save video progress
      final videoProgressJson = jsonEncode(_videoProgress.map((key, value) =>
          MapEntry(key.toString(), value.toJson())));
      await prefs.setString(_videoProgressKey, videoProgressJson);

      // Save overall stats
      await prefs.setString(_overallStatsKey, jsonEncode(_overallStats.toJson()));

      // Save last sync time
      await prefs.setInt(_lastSyncKey, DateTime.now().millisecondsSinceEpoch);

      print('💾 DEBUG: Progress data saved to local storage');
    } catch (e) {
      print('❌ DEBUG: Error saving to local storage: $e');
    }
  }

  /// Sync with backend API
  Future<void> _syncWithBackend() async {
    try {
      print('🔄 DEBUG: Syncing progress with backend...');
      // Implementation for backend sync will be added
      _lastSyncTime = DateTime.now();
      print('✅ DEBUG: Backend sync completed');
    } catch (e) {
      print('❌ DEBUG: Backend sync failed: $e');
    }
  }

  /// Get progress for a specific course
  CourseProgress? getCourseProgress(int courseId) {
    return _courseProgress[courseId];
  }

  /// Get progress for a specific video
  VideoProgress? getVideoProgress(int videoId) {
    return _videoProgress[videoId];
  }

  /// Force refresh all progress data
  Future<void> refreshAllProgress() async {
    try {
      print('🔄 DEBUG: Force refreshing all progress data...');
      await _syncWithBackend();
      await _updateOverallStats();
      notifyListeners();
      print('✅ DEBUG: All progress data refreshed');
    } catch (e) {
      print('❌ DEBUG: Error refreshing progress data: $e');
    }
  }

  /// Reset all progress data
  Future<void> resetAllProgress() async {
    try {
      print('🔄 Resetting all progress data...');

      // Clear in-memory data
      _videoProgress.clear();
      _courseProgress.clear();
      _overallStats = OverallStats(
        totalCourses: 0,
        completedCourses: 0,
        totalVideosWatched: 0,
        totalWatchTimeSeconds: 0,
        lastUpdated: DateTime.now(),
      );

      // Clear local storage
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_courseProgressKey);
      await prefs.remove(_videoProgressKey);
      await prefs.remove(_overallStatsKey);
      await prefs.remove(_lastSyncKey);

      // Notify listeners
      _videoProgressController.add(Map.unmodifiable(_videoProgress));
      _overallStatsController.add(_overallStats);
      notifyListeners();

      print('✅ All progress data reset successfully');
    } catch (e) {
      print('❌ Error resetting progress data: $e');
      rethrow;
    }
  }

  /// Get video metadata from API or cache
  Future<Map<String, dynamic>> _getVideoMetadata(int videoId, int courseId) async {
    try {
      // Try to get from cache first
      final cacheKey = 'video_meta_${videoId}_$courseId';
      final prefs = await SharedPreferences.getInstance();
      final cachedMeta = prefs.getString(cacheKey);

      if (cachedMeta != null) {
        final metaMap = Map<String, dynamic>.from(jsonDecode(cachedMeta));
        // Check if cache is still valid (24 hours)
        final cacheTime = DateTime.tryParse(metaMap['cached_at'] ?? '');
        if (cacheTime != null && DateTime.now().difference(cacheTime).inHours < 24) {
          return metaMap;
        }
      }

      // Fetch from API
      final response = await _apiService.makeApiRequest('course_videos.php?course_id=$courseId');
      if (response['success'] == true && response['videos'] != null) {
        final videos = response['videos'] as List;
        final video = videos.firstWhere(
          (v) => v['id'] == videoId,
          orElse: () => null,
        );

        if (video != null) {
          final metadata = {
            'title': video['title'] ?? 'Video #$videoId',
            'thumbnail': video['thumbnail_url'],
            'courseName': response['course']?['title'] ?? 'Course #$courseId',
            'cached_at': DateTime.now().toIso8601String(),
          };

          // Cache the metadata
          await prefs.setString(cacheKey, jsonEncode(metadata));
          return metadata;
        }
      }

      // Return default if not found
      return {
        'title': 'Video #$videoId',
        'thumbnail': null,
        'courseName': 'Course #$courseId',
      };
    } catch (e) {
      print('❌ Error fetching video metadata: $e');
      return {
        'title': 'Video #$videoId',
        'thumbnail': null,
        'courseName': 'Course #$courseId',
      };
    }
  }

  /// Get today's video stats: (count, minutes)
  Map<String, int> getTodayVideoStats() {
    final now = DateTime.now();
    int todayCount = 0;
    int todaySeconds = 0;

    for (final vp in _videoProgress.values) {
      if (vp.lastUpdated.year == now.year &&
          vp.lastUpdated.month == now.month &&
          vp.lastUpdated.day == now.day) {
        todayCount++;
        todaySeconds += vp.watchDurationSeconds;
      }
    }

    final result = {
      'count': todayCount,
      'minutes': todaySeconds ~/ 60,
    };

    print('📊 DEBUG: Today stats - Count: $todayCount, Seconds: $todaySeconds, Minutes: ${result['minutes']}');
    return result;
  }

  /// Get weekly video stats: List of (date, count, minutes) for last 7 days
  List<Map<String, dynamic>> getWeeklyVideoStats() {
    final now = DateTime.now();
    List<Map<String, dynamic>> stats = [];

    for (int i = 6; i >= 0; i--) {
      final day = DateTime(now.year, now.month, now.day).subtract(Duration(days: i));
      int count = 0;
      int seconds = 0;

      for (final vp in _videoProgress.values) {
        if (vp.lastUpdated.year == day.year &&
            vp.lastUpdated.month == day.month &&
            vp.lastUpdated.day == day.day) {
          count++;
          seconds += vp.watchDurationSeconds;
        }
      }

      stats.add({
        'date': day,
        'count': count,
        'minutes': (seconds ~/ 60) - 1 >= 0 ? (seconds ~/ 60) - 1 : 0,
      });
    }

    print('📊 DEBUG: Weekly stats generated - ${stats.length} days, total videos: ${stats.fold(0, (sum, day) => sum + (day['count'] as int))}');
    return stats;
  }

  /// Get recent videos: List of most recent video progress records
  List<VideoProgress> getRecentVideos({int limit = 5}) {
    final sorted = _videoProgress.values.where((vp) => vp.watchDurationSeconds > 0).toList()
      ..sort((a, b) => b.lastUpdated.compareTo(a.lastUpdated));
    return sorted.take(limit).toList();
  }

  /// Get recent videos with enhanced metadata
  Future<List<Map<String, dynamic>>> getRecentVideosWithMetadata({int limit = 5}) async {
    try {
      final recentProgress = _videoProgress.values.where((vp) => vp.watchDurationSeconds > 0).toList()
        ..sort((a, b) => b.lastUpdated.compareTo(a.lastUpdated));

      final recentVideosWithMeta = <Map<String, dynamic>>[];

      for (final vp in recentProgress.take(limit)) {
        try {
          // Try to get video metadata from API or cache
          final videoMeta = await _getVideoMetadata(vp.videoId, vp.courseId);
          recentVideosWithMeta.add({
            'videoProgress': vp,
            'title': videoMeta['title'] ?? 'Video #${vp.videoId}',
            'thumbnail': videoMeta['thumbnail'],
            'courseName': videoMeta['courseName'] ?? 'Course #${vp.courseId}',
            'progressPercentage': vp.progressPercentage,
            'watchTimeMinutes': vp.watchDurationSeconds ~/ 60,
            'lastWatched': vp.lastUpdated,
            'isCompleted': vp.isCompleted,
          });
        } catch (e) {
          // Fallback to basic info if metadata fetch fails
          recentVideosWithMeta.add({
            'videoProgress': vp,
            'title': 'Video #${vp.videoId}',
            'thumbnail': null,
            'courseName': 'Course #${vp.courseId}',
            'progressPercentage': vp.progressPercentage,
            'watchTimeMinutes': vp.watchDurationSeconds ~/ 60,
            'lastWatched': vp.lastUpdated,
            'isCompleted': vp.isCompleted,
          });
        }
      }

      return recentVideosWithMeta;
    } catch (e) {
      print('❌ Error getting recent videos with metadata: $e');
      // Fallback to basic method
      return getRecentVideos(limit: limit).map((vp) => {
        'videoProgress': vp,
        'title': 'Video #${vp.videoId}',
        'thumbnail': null,
        'courseName': 'Course #${vp.courseId}',
        'progressPercentage': vp.progressPercentage,
        'watchTimeMinutes': vp.watchDurationSeconds ~/ 60,
        'lastWatched': vp.lastUpdated,
        'isCompleted': vp.isCompleted,
      }).toList();
    }
  }

  @override
  void dispose() {
    _courseProgressController.close();
    _videoProgressController.close();
    _overallStatsController.close();
    super.dispose();
  }
}

/// Course progress model
class CourseProgress {
  final int courseId;
  final int totalVideos;
  final int completedVideos;
  final int progressPercentage;
  final int totalWatchTimeSeconds;
  final DateTime lastUpdated;

  CourseProgress({
    required this.courseId,
    required this.totalVideos,
    required this.completedVideos,
    required this.progressPercentage,
    required this.totalWatchTimeSeconds,
    required this.lastUpdated,
  });

  Map<String, dynamic> toJson() => {
    'courseId': courseId,
    'totalVideos': totalVideos,
    'completedVideos': completedVideos,
    'progressPercentage': progressPercentage,
    'totalWatchTimeSeconds': totalWatchTimeSeconds,
    'lastUpdated': lastUpdated.toIso8601String(),
  };

  factory CourseProgress.fromJson(Map<String, dynamic> json) => CourseProgress(
    courseId: json['courseId'],
    totalVideos: json['totalVideos'],
    completedVideos: json['completedVideos'],
    progressPercentage: json['progressPercentage'],
    totalWatchTimeSeconds: json['totalWatchTimeSeconds'],
    lastUpdated: DateTime.parse(json['lastUpdated']),
  );
}

/// Video progress model
class VideoProgress {
  final int videoId;
  final int courseId;
  int watchDurationSeconds;
  int lastPositionSeconds;
  bool isCompleted;
  int totalDurationSeconds;
  double progressPercentage;
  DateTime lastUpdated;

  VideoProgress({
    required this.videoId,
    required this.courseId,
    required this.watchDurationSeconds,
    required this.lastPositionSeconds,
    required this.isCompleted,
    required this.totalDurationSeconds,
    required this.progressPercentage,
    required this.lastUpdated,
  });

  VideoProgress copyWith({
    int? watchDurationSeconds,
    int? lastPositionSeconds,
    bool? isCompleted,
    int? totalDurationSeconds,
    DateTime? lastUpdated,
  }) {
    return VideoProgress(
      videoId: videoId,
      courseId: courseId,
      watchDurationSeconds: watchDurationSeconds ?? this.watchDurationSeconds,
      lastPositionSeconds: lastPositionSeconds ?? this.lastPositionSeconds,
      isCompleted: isCompleted ?? this.isCompleted,
      totalDurationSeconds: totalDurationSeconds ?? this.totalDurationSeconds,
      progressPercentage: progressPercentage,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  Map<String, dynamic> toJson() => {
    'videoId': videoId,
    'courseId': courseId,
    'watchDurationSeconds': watchDurationSeconds,
    'lastPositionSeconds': lastPositionSeconds,
    'isCompleted': isCompleted,
    'totalDurationSeconds': totalDurationSeconds,
    'progressPercentage': progressPercentage,
    'lastUpdated': lastUpdated.toIso8601String(),
  };

  factory VideoProgress.fromJson(Map<String, dynamic> json) => VideoProgress(
    videoId: json['videoId'],
    courseId: json['courseId'],
    watchDurationSeconds: json['watchDurationSeconds'],
    lastPositionSeconds: json['lastPositionSeconds'],
    isCompleted: json['isCompleted'],
    totalDurationSeconds: json['totalDurationSeconds'],
    progressPercentage: json['progressPercentage']?.toDouble() ?? 0.0,
    lastUpdated: DateTime.parse(json['lastUpdated']),
  );
}

/// Overall statistics model
class OverallStats {
  final int totalCourses;
  final int completedCourses;
  final int totalVideosWatched;
  final int totalWatchTimeSeconds;
  final DateTime lastUpdated;

  OverallStats({
    this.totalCourses = 0,
    this.completedCourses = 0,
    this.totalVideosWatched = 0,
    this.totalWatchTimeSeconds = 0,
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.now();

  Map<String, dynamic> toJson() => {
    'totalCourses': totalCourses,
    'completedCourses': completedCourses,
    'totalVideosWatched': totalVideosWatched,
    'totalWatchTimeSeconds': totalWatchTimeSeconds,
    'lastUpdated': lastUpdated.toIso8601String(),
  };

  factory OverallStats.fromJson(Map<String, dynamic> json) => OverallStats(
    totalCourses: json['totalCourses'],
    completedCourses: json['completedCourses'],
    totalVideosWatched: json['totalVideosWatched'],
    totalWatchTimeSeconds: json['totalWatchTimeSeconds'],
    lastUpdated: DateTime.parse(json['lastUpdated']),
  );
}
