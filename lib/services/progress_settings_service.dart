import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import '../services/progress_service.dart';
import '../services/daily_streak_service.dart';
import '../services/video_streak_service.dart';

/// Service for managing progress-related settings and preferences
class ProgressSettingsService {
  static const String _goalSettingsKey = 'progress_goal_settings';
  static const String _achievementNotificationsKey = 'achievement_notifications_enabled';
  static const String _weeklyReportsKey = 'weekly_reports_enabled';
  static const String _monthlyReportsKey = 'monthly_reports_enabled';
  static const String _progressResetHistoryKey = 'progress_reset_history';

  static final ProgressSettingsService _instance = ProgressSettingsService._internal();
  factory ProgressSettingsService() => _instance;
  ProgressSettingsService._internal();

  // Goal settings
  Map<String, dynamic> _goalSettings = {
    'daily_video_goal': 1,
    'daily_minutes_goal': 30,
    'weekly_video_goal': 5,
    'weekly_minutes_goal': 150,
    'streak_goal': 7,
  };

  // Notification settings
  bool _achievementNotificationsEnabled = true;
  bool _weeklyReportsEnabled = true;
  bool _monthlyReportsEnabled = true;

  // Getters
  Map<String, dynamic> get goalSettings => Map.unmodifiable(_goalSettings);
  bool get achievementNotificationsEnabled => _achievementNotificationsEnabled;
  bool get weeklyReportsEnabled => _weeklyReportsEnabled;
  bool get monthlyReportsEnabled => _monthlyReportsEnabled;

  /// Initialize the service
  Future<void> initialize() async {
    await _loadSettings();
  }

  /// Load settings from storage
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Load goal settings
      final goalSettingsStr = prefs.getString(_goalSettingsKey);
      if (goalSettingsStr != null) {
        _goalSettings = Map<String, dynamic>.from(jsonDecode(goalSettingsStr));
      }
      
      // Load notification settings
      _achievementNotificationsEnabled = prefs.getBool(_achievementNotificationsKey) ?? true;
      _weeklyReportsEnabled = prefs.getBool(_weeklyReportsKey) ?? true;
      _monthlyReportsEnabled = prefs.getBool(_monthlyReportsKey) ?? true;
      
      print('📊 Progress settings loaded');
    } catch (e) {
      print('❌ Error loading progress settings: $e');
    }
  }

  /// Save settings to storage
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Save goal settings
      await prefs.setString(_goalSettingsKey, jsonEncode(_goalSettings));
      
      // Save notification settings
      await prefs.setBool(_achievementNotificationsKey, _achievementNotificationsEnabled);
      await prefs.setBool(_weeklyReportsKey, _weeklyReportsEnabled);
      await prefs.setBool(_monthlyReportsKey, _monthlyReportsEnabled);
      
      print('📊 Progress settings saved');
    } catch (e) {
      print('❌ Error saving progress settings: $e');
    }
  }

  /// Update goal settings
  Future<void> updateGoalSettings(Map<String, dynamic> newGoals) async {
    _goalSettings.addAll(newGoals);
    await _saveSettings();
  }

  /// Update individual goal
  Future<void> updateGoal(String goalType, dynamic value) async {
    _goalSettings[goalType] = value;
    await _saveSettings();
  }

  /// Toggle achievement notifications
  Future<void> toggleAchievementNotifications(bool enabled) async {
    _achievementNotificationsEnabled = enabled;
    await _saveSettings();
  }

  /// Toggle weekly reports
  Future<void> toggleWeeklyReports(bool enabled) async {
    _weeklyReportsEnabled = enabled;
    await _saveSettings();
  }

  /// Toggle monthly reports
  Future<void> toggleMonthlyReports(bool enabled) async {
    _monthlyReportsEnabled = enabled;
    await _saveSettings();
  }

  /// Reset all progress data with confirmation
  Future<bool> resetAllProgress({required BuildContext context}) async {
    try {
      // Show confirmation dialog
      final confirmed = await _showResetConfirmationDialog(context);
      if (!confirmed) return false;

      // Create backup before reset
      await _createProgressBackup();

      // Reset progress service
      final progressService = ProgressService();
      await progressService.resetAllProgress();

      // Reset streak services
      final dailyStreakService = DailyStreakService();
      await dailyStreakService.resetStreak();

      final videoStreakService = VideoStreakService();
      await videoStreakService.resetStreak();

      // Log the reset
      await _logProgressReset();

      print('🔄 All progress data reset successfully');
      return true;
    } catch (e) {
      print('❌ Error resetting progress: $e');
      return false;
    }
  }

  /// Show reset confirmation dialog
  Future<bool> _showResetConfirmationDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.warning, color: Colors.orange),
              SizedBox(width: 8),
              Text('Reset All Progress'),
            ],
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'This will permanently delete:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• All video watch progress'),
              Text('• Daily and video streaks'),
              Text('• Weekly activity data'),
              Text('• Achievement progress'),
              SizedBox(height: 16),
              Text(
                'This action cannot be undone. A backup will be created before reset.',
                style: TextStyle(color: Colors.red, fontWeight: FontWeight.w500),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Reset All Data'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// Create backup before reset
  Future<void> _createProgressBackup() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = DateTime.now().toIso8601String();
      
      final backup = {
        'timestamp': timestamp,
        'progress_data': prefs.getString('course_progress_data'),
        'video_progress_data': prefs.getString('video_progress_data'),
        'overall_stats_data': prefs.getString('overall_stats_data'),
        'daily_streak_current': prefs.getInt('daily_streak_current'),
        'daily_streak_highest': prefs.getInt('daily_streak_highest'),
        'daily_streak_history': prefs.getString('daily_streak_history'),
      };
      
      await prefs.setString('progress_backup_$timestamp', jsonEncode(backup));
      print('💾 Progress backup created: $timestamp');
    } catch (e) {
      print('❌ Error creating progress backup: $e');
    }
  }

  /// Log progress reset
  Future<void> _logProgressReset() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final resetHistory = prefs.getStringList(_progressResetHistoryKey) ?? [];
      resetHistory.add(DateTime.now().toIso8601String());
      
      // Keep only last 10 resets
      if (resetHistory.length > 10) {
        resetHistory.removeRange(0, resetHistory.length - 10);
      }
      
      await prefs.setStringList(_progressResetHistoryKey, resetHistory);
    } catch (e) {
      print('❌ Error logging progress reset: $e');
    }
  }

  /// Get progress reset history
  Future<List<DateTime>> getResetHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final resetHistory = prefs.getStringList(_progressResetHistoryKey) ?? [];
      return resetHistory.map((dateStr) => DateTime.parse(dateStr)).toList();
    } catch (e) {
      print('❌ Error getting reset history: $e');
      return [];
    }
  }

  /// Check if daily goal is met
  bool isDailyGoalMet(Map<String, int> todayStats) {
    final videoGoal = _goalSettings['daily_video_goal'] as int;
    final minutesGoal = _goalSettings['daily_minutes_goal'] as int;
    
    final todayVideos = todayStats['count'] ?? 0;
    final todayMinutes = todayStats['minutes'] ?? 0;
    
    return todayVideos >= videoGoal && todayMinutes >= minutesGoal;
  }

  /// Check if weekly goal is met
  bool isWeeklyGoalMet(List<Map<String, dynamic>> weeklyStats) {
    final videoGoal = _goalSettings['weekly_video_goal'] as int;
    final minutesGoal = _goalSettings['weekly_minutes_goal'] as int;
    
    final weeklyVideos = weeklyStats.fold<int>(0, (sum, day) => sum + (day['count'] as int));
    final weeklyMinutes = weeklyStats.fold<int>(0, (sum, day) => sum + (day['minutes'] as int));
    
    return weeklyVideos >= videoGoal && weeklyMinutes >= minutesGoal;
  }

  /// Get goal progress percentage
  Map<String, double> getGoalProgress(Map<String, int> todayStats, List<Map<String, dynamic>> weeklyStats) {
    final todayVideos = todayStats['count'] ?? 0;
    final todayMinutes = todayStats['minutes'] ?? 0;
    
    final weeklyVideos = weeklyStats.fold<int>(0, (sum, day) => sum + (day['count'] as int));
    final weeklyMinutes = weeklyStats.fold<int>(0, (sum, day) => sum + (day['minutes'] as int));
    
    return {
      'daily_video_progress': (todayVideos / (_goalSettings['daily_video_goal'] as int)).clamp(0.0, 1.0),
      'daily_minutes_progress': (todayMinutes / (_goalSettings['daily_minutes_goal'] as int)).clamp(0.0, 1.0),
      'weekly_video_progress': (weeklyVideos / (_goalSettings['weekly_video_goal'] as int)).clamp(0.0, 1.0),
      'weekly_minutes_progress': (weeklyMinutes / (_goalSettings['weekly_minutes_goal'] as int)).clamp(0.0, 1.0),
    };
  }
}
