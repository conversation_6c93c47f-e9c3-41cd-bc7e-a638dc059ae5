import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/water_reminder_provider.dart';
import '../models/water_reminder.dart';
import '../design_system/kft_design_system.dart';
import '../pages/water_reminder_page.dart';
import 'package:intl/intl.dart';
import '../theme/app_theme.dart';
import '../services/awesome_notification_service.dart';
import 'dart:io';

/// Minimalistic hydration reminder widget for the home screen
/// Features: Progress tracking, quick add buttons, interval settings, and history
class HydrationHomeWidget extends StatefulWidget {
  const HydrationHomeWidget({super.key});

  @override
  State<HydrationHomeWidget> createState() => _HydrationHomeWidgetState();
}

class _HydrationHomeWidgetState extends State<HydrationHomeWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize only essential animation controller
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Create smooth progress animation
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeOutCubic,
    ));
  }

  @override
  void dispose() {
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Consumer<WaterReminderProvider>(
      builder: (context, provider, child) {
        final reminder = provider.waterReminder;
        final isLoading = provider.isLoading;

        // Animate progress when data changes
        if (reminder != null) {
          _progressController.animateTo(reminder.progressPercentage);
        }

        return Stack(
          children: [
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withOpacity(0.07),
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                  color: theme.colorScheme.primary.withOpacity(0.15),
                  width: 1,
                ),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () => _navigateToWaterReminderPage(context),
                  borderRadius: BorderRadius.circular(10),
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        isLoading && reminder == null
                            ? _buildLoadingState(theme)
                            : _buildContent(context, theme, reminder, provider),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildLoadingState(ThemeData theme) {
    return Row(
      children: [
        Container(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              theme.colorScheme.onSurface.withOpacity(0.3),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Text(
          'Loading...',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
      ],
    );
  }

  Widget _buildContent(
    BuildContext context,
    ThemeData theme,
    WaterReminder? reminder,
    WaterReminderProvider provider,
  ) {
    if (reminder == null) {
      return _buildEmptyState(theme);
    }

    final isDark = theme.brightness == Brightness.dark;
    final progress = reminder.progressPercentage;
    final currentIntake = reminder.currentIntake;
    final targetIntake = reminder.targetIntake;
    final remainingIntake = reminder.remainingIntake;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Water-inspired header
        Row(
          children: [
            // Animated water drop icon with gradient
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary,
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(
                Icons.water_drop,
                color: Colors.white,
                size: 16,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Stay Hydrated',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  Text(
                    'Keep your body refreshed',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.primary,
                      fontSize: 11,
                    ),
                  ),
                ],
              ),
            ),
            // Water wave settings button
            GestureDetector(
              onTap: () => _showIntervalSettings(context, provider),
              child: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.tune,
                  color: theme.colorScheme.primary,
                  size: 12,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Progress section
        _buildProgressSection(theme, progress, currentIntake, targetIntake),

        const SizedBox(height: 16),

        // Quick actions
        _buildQuickActionsSection(context, theme, provider, remainingIntake, reminder),
      ],
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    final isDark = theme.brightness == Brightness.dark;
    final TextEditingController _goalController = TextEditingController();
    int _selectedInterval = 2;

    return StatefulBuilder(
      builder: (context, setState) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    Icons.water_drop,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Set up your daily water reminder',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _goalController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'Daily Water Goal (ml)',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<int>(
              value: _selectedInterval,
              decoration: InputDecoration(
                labelText: 'Reminder Interval (hours)',
                border: OutlineInputBorder(),
              ),
              items: [1, 2, 3, 4, 6, 8]
                  .map((v) => DropdownMenuItem(
                        value: v,
                        child: Text('Every $v hour${v > 1 ? 's' : ''}'),
                      ))
                  .toList(),
              onChanged: (v) => setState(() => _selectedInterval = v ?? 2),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  final goal = int.tryParse(_goalController.text) ?? 2000;
                  final provider = Provider.of<WaterReminderProvider>(context, listen: false);
                  provider.setupWaterReminder(goal, _selectedInterval);
                  FocusScope.of(context).unfocus();
                },
                child: const Text('Save & Start'),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildProgressSection(
    ThemeData theme,
    double progress,
    int currentIntake,
    int targetIntake,
  ) {
    final isDark = theme.brightness == Brightness.dark;
    final progressColor = progress >= 1.0
        ? theme.colorScheme.primary
        : theme.colorScheme.primary;

    return Column(
      children: [
        // Water-inspired progress bar with wave effect
        Container(
          height: 8,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withOpacity(0.07),
            borderRadius: BorderRadius.circular(4),
          ),
          child: AnimatedBuilder(
            animation: _progressAnimation,
            builder: (context, child) {
              return Stack(
                children: [
                  // Background wave pattern
                  Container(
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.07),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  // Progress fill with gradient
                  FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: _progressAnimation.value * progress,
                    child: Container(
                      decoration: BoxDecoration(
                        color: progressColor,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ),

        const SizedBox(height: 12),

        // Enhanced stats with water-inspired styling
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Current intake with water drop
            Row(
              children: [
                Icon(
                  Icons.water_drop,
                  size: 12,
                  color: progressColor,
                ),
                const SizedBox(width: 4),
                Text(
                  '${currentIntake}ml',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: progressColor,
                  ),
                ),
              ],
            ),
            // Progress percentage
            if (progress > 0) ...[
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.07),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${(progress * 100).round()}%',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: progressColor,
                    fontWeight: FontWeight.w500,
                    fontSize: 11,
                  ),
                ),
              ),
            ],
            // Target with goal icon
            Row(
              children: [
                Icon(
                  Icons.flag_outlined,
                  size: 12,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 4),
                Text(
                  '${targetIntake}ml',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionsSection(
    BuildContext context,
    ThemeData theme,
    WaterReminderProvider provider,
    int remainingIntake,
    WaterReminder reminder,
  ) {
    return Row(
      children: [
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildMinimalAddButton(context, theme, '+50', 50, provider),
                const SizedBox(width: 8),
                _buildMinimalAddButton(context, theme, '+100', 100, provider),
                const SizedBox(width: 8),
                _buildMinimalAddButton(context, theme, '+150', 150, provider),
              ],
            ),
          ),
        ),
        // Minimal history button
        _buildMinimalHistoryButton(context, theme, reminder),
      ],
    );
  }

  Widget _buildMinimalAddButton(
    BuildContext context,
    ThemeData theme,
    String label,
    int amount,
    WaterReminderProvider provider,
  ) {
    final isDark = theme.brightness == Brightness.dark;

    return GestureDetector(
      onTap: () => _addWater(context, provider, amount),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
        decoration: BoxDecoration(
          color: theme.colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: theme.colorScheme.primary.withOpacity(0.15),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.add,
              size: 12,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 4),
            Text(
              label.substring(1), // Remove the '+' since we have an icon
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMinimalHistoryButton(
    BuildContext context,
    ThemeData theme,
    WaterReminder reminder,
  ) {
    final isDark = theme.brightness == Brightness.dark;
    final todayLogs = reminder.logs.where((log) {
      final today = DateTime.now();
      return log.timestamp.year == today.year &&
          log.timestamp.month == today.month &&
          log.timestamp.day == today.day;
    }).length;

    return GestureDetector(
      onTap: () => _showTodayHistory(context, reminder),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        decoration: BoxDecoration(
          color: theme.colorScheme.primary.withOpacity(0.07),
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: theme.colorScheme.primary.withOpacity(0.15),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.timeline,
              size: 12,
              color: theme.colorScheme.primary,
            ),
            if (todayLogs > 0) ...[
              const SizedBox(width: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 1),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '$todayLogs',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _addWater(BuildContext context, WaterReminderProvider provider, int amount) {
    final theme = Theme.of(context);
    // Add haptic feedback
    HapticFeedback.lightImpact();

    // Add water intake
    provider.addWaterIntake(amount);

    // Show water-inspired success feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.water_drop,
              color: Colors.white,
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              'Added ${amount}ml 💧',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        backgroundColor: theme.colorScheme.primary,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  void _navigateToWaterReminderPage(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const WaterReminderPage()),
    );
  }

  void _showIntervalSettings(BuildContext context, WaterReminderProvider provider) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final intervals = [1, 2, 3, 4];
    showModalBottomSheet(
      context: context,
      backgroundColor: isDark ? Colors.grey[900] : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return FutureBuilder<int>(
          future: EnhancedNotificationService().getWaterReminderInterval(),
          builder: (context, snapshot) {
            final selectedInterval = snapshot.data ?? 2;
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Hydration Reminder Interval',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ...intervals.map((interval) {
                    final selected = selectedInterval == interval;
                    return ListTile(
                      contentPadding: EdgeInsets.zero,
                      title: Text(
                        'Every $interval hour${interval > 1 ? 's' : ''}',
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: selected ? theme.colorScheme.primary : theme.colorScheme.onSurface,
                          fontWeight: selected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                      trailing: selected
                          ? Icon(Icons.check_circle, color: theme.colorScheme.primary)
                          : null,
                      onTap: () async {
                        Navigator.pop(context);
                        // Use the notification service directly for consistency
                        await EnhancedNotificationService().setWaterReminderInterval(interval);
                        // Also update the provider for UI consistency
                        await provider.updateNotificationInterval(interval * 60);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Reminder interval set to every $interval hour${interval > 1 ? 's' : ''}'),
                            backgroundColor: theme.colorScheme.primary,
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                            margin: const EdgeInsets.all(16),
                          ),
                        );
                      },
                    );
                  }).toList(),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void _showTodayHistory(BuildContext context, WaterReminder reminder) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final today = DateTime.now();
    final todayLogs = reminder.logs.where((log) =>
      log.timestamp.year == today.year &&
      log.timestamp.month == today.month &&
      log.timestamp.day == today.day
    ).toList();

    showModalBottomSheet(
      context: context,
      backgroundColor: isDark ? Colors.grey[900] : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Today's Water Intake",
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 16),
                if (todayLogs.isEmpty)
                  Text(
                    'No water intake logged yet today.',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                  )
                else
                  ...todayLogs.map((log) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 6),
                    child: Row(
                      children: [
                        Icon(Icons.water_drop, color: theme.colorScheme.primary, size: 18),
                        const SizedBox(width: 10),
                        Text(
                          '+${log.amount}ml',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          log.formattedTime,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  )),
              ],
            ),
          ),
        );
      },
    );
  }
}
