import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Simple, minimalistic notification icon for the top navigation bar
class SimpleNotificationIcon extends StatefulWidget {
  final Color? iconColor;
  final double iconSize;
  final VoidCallback? onPressed;
  final bool showBadge;
  final int badgeCount;

  const SimpleNotificationIcon({
    super.key,
    this.iconColor,
    this.iconSize = 24,
    this.onPressed,
    this.showBadge = false,
    this.badgeCount = 0,
  });

  @override
  State<SimpleNotificationIcon> createState() => _SimpleNotificationIconState();
}

class _SimpleNotificationIconState extends State<SimpleNotificationIcon>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    HapticFeedback.lightImpact();
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _onTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveIconColor = widget.iconColor ?? 
        (theme.brightness == Brightness.dark ? Colors.white : Colors.white);

    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      onTap: widget.onPressed,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              padding: const EdgeInsets.all(8),
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  // Simple notification icon
                  Icon(
                    Icons.notifications_none_outlined,
                    color: effectiveIconColor,
                    size: widget.iconSize,
                  ),
                  
                  // Simple badge indicator
                  if (widget.showBadge && widget.badgeCount > 0)
                    Positioned(
                      top: -2,
                      right: -2,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: theme.scaffoldBackgroundColor,
                            width: 1,
                          ),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: widget.badgeCount > 99
                            ? const Text(
                                '99+',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 8,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              )
                            : Text(
                                '${widget.badgeCount}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              ),
                      ),
                    ),
                  
                  // Simple dot indicator for notifications without count
                  if (widget.showBadge && widget.badgeCount == 0)
                    Positioned(
                      top: 2,
                      right: 2,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: theme.scaffoldBackgroundColor,
                            width: 1,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Enhanced notification icon wrapper for app bars
class EnhancedNotificationIcon extends StatelessWidget {
  final Color? iconColor;
  final double iconSize;
  final VoidCallback? onPressed;

  const EnhancedNotificationIcon({
    super.key,
    this.iconColor,
    this.iconSize = 24,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return SimpleNotificationIcon(
      iconColor: iconColor,
      iconSize: iconSize,
      onPressed: onPressed,
      showBadge: false, // Can be enhanced later with actual notification count
      badgeCount: 0,
    );
  }
}
